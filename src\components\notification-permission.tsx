'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { isNotificationSupported } from '@/lib/notification';
import { toast } from 'sonner';

export function NotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission | null>(
    null
  );
  const [supported, setSupported] = useState(false);

  useEffect(() => {
    // Check if notifications are supported
    const notificationsSupported = isNotificationSupported();
    setSupported(notificationsSupported);

    if (notificationsSupported) {
      setPermission(Notification.permission);
    }
  }, []);

  const handleRequestPermission = async () => {
    try {
      // Request permission directly using the Notification API
      const result = await Notification.requestPermission();
      setPermission(result);

      if (result === 'granted') {
        toast.success('Notification permission granted!');
      } else if (result === 'denied') {
        // Show instructions for enabling in browser settings
        toast.error(
          'Permission denied. Please enable notifications in your browser settings.',
          { duration: 5000 }
        );
      } else {
        toast.info('Notification permission was dismissed');
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      toast.error('Failed to request notification permission');
    }
  };

  if (!supported) {
    return null;
  }

  if (permission === 'granted') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 p-4 bg-background border rounded-lg shadow-lg z-50 max-w-sm">
      <h3 className="font-medium mb-2">Enable Notifications</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Get real-time updates and important alerts directly to your device.
      </p>
      <Button onClick={handleRequestPermission} size="sm">
        Enable Notifications
      </Button>
      {permission === 'denied' && (
        <div className="mt-2 text-xs text-amber-500">
          Notifications are blocked. Please enable them in your browser
          settings.
        </div>
      )}
    </div>
  );
}
