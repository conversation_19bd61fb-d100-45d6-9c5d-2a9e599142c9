'use client';

import { useState } from 'react';
import { BookUser } from 'lucide-react';
import Staff from './data-table';
import Department from './department/data-table';
import { Badge } from '@/components/ui/badge';

export default function StaffPage() {
  const [open, setOpen] = useState(false);
  const [openDept, setOpenDept] = useState(false);
  const [switchView, setSwitchView] = useState(false);

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <BookUser className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        {switchView ? 'Department Management' : 'Staff Management'}
      </h2>
      <div className="space-x-2">
        {!switchView && (
          <Badge className="h-7 cursor-pointer" onClick={() => setOpen(true)}>
            Add staff
          </Badge>
        )}
        {!switchView && (
          <Badge
            variant={switchView ? 'default' : 'outline'}
            className="h-7 cursor-pointer"
            onClick={() => setSwitchView(true)}
          >
            Manage Department
          </Badge>
        )}
        {switchView && (
          <Badge
            variant={switchView ? 'default' : 'outline'}
            className="h-7 cursor-pointer"
            onClick={() => setOpenDept(true)}
          >
            Add Department
          </Badge>
        )}
        {switchView && (
          <Badge
            variant={!switchView ? 'default' : 'outline'}
            className="h-7 cursor-pointer"
            onClick={() => setSwitchView(false)}
          >
            Staff Management
          </Badge>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        {!switchView ? (
          <Staff openCreate={open} setOpenCreate={setOpen} />
        ) : (
          <Department openCreate={openDept} setOpenCreate={setOpenDept} />
        )}
      </div>
    </>
  );
}
