import { Modal } from '@/components/common/modal';
import React, { useState } from 'react';
import { ModalProps } from '@/components/types';
import { numberFormat, cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  CreditCard,
  Receipt,
  User,
  ArrowDownCircle,
  ArrowUpCircle,
} from 'lucide-react';
import dayjs from 'dayjs';
import { StatusBadge } from '@/components/common/status-badge';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [activeTab, setActiveTab] = useState(0);

  if (!data) return null;

  // Transaction Details Tab Content
  const TransactionInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Basic Information
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Amount Paid</p>
            <p className="font-medium text-lg">{numberFormat(data.amount)}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Reference</p>
            <p className="font-medium">{data.reference || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Date</p>
            <p className="font-medium">
              {dayjs(data.createdAt).format('MMMM D, YYYY h:mm A')}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Status</p>
            <div className="mt-1">
              {StatusBadge({
                status: data.status.toLowerCase(),
              })}
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Transaction Details
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Type</p>
            <div className="flex items-center gap-2 mt-1">
              {data.type === 'INBOUND' ? (
                <>
                  <ArrowDownCircle className="w-4 h-4 text-green-500" />
                  <span className="font-medium">Inbound</span>
                </>
              ) : (
                <>
                  <ArrowUpCircle className="w-4 h-4 text-red-500" />
                  <span className="font-medium">Outbound</span>
                </>
              )}
            </div>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Mode</p>
            <p className="font-medium">{data.mode || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Service Amount</p>
            <p className="font-medium text-lg">
              {numberFormat(data.amount - data.charges)}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Payment Charges</p>
            <p className="font-medium text-lg">{numberFormat(data.charges)}</p>
          </div>
          {data.remarks && (
            <div className="sm:col-span-2">
              <p className="text-gray-500 dark:text-gray-400">Description</p>
              <p className="font-medium">{data.remarks}</p>
            </div>
          )}
        </div>
      </div>

      {data.metadata && (
        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Additional Information
          </h3>
          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
            <pre className="text-xs overflow-auto whitespace-pre-wrap">
              {typeof data.metadata === 'string'
                ? data.metadata
                : JSON.stringify(data.metadata, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );

  // User Information Tab Content (if available)
  const UserInfo = () => (
    <div className="space-y-6">
      {data.user ? (
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Full Name/UHID</p>
            <p className="font-medium">
              {(data.user?.title ? data.user.title + ' ' : '') +
                (data.user?.firstName ? data.user.firstName + ' ' : '') +
                (data.user?.lastName || '') || data.user.uhid}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Email</p>
            <p className="font-medium">{data.user?.emailAddress || 'N/A'}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Phone</p>
            <p className="font-medium">{data.user?.phoneNumber || 'N/A'}</p>
          </div>
          {data.user?.address && (
            <div className="sm:col-span-2">
              <p className="text-gray-500 dark:text-gray-400">Address</p>
              <p className="font-medium">{data.user.address}</p>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-6 text-gray-500">
          No user information available for this transaction
        </div>
      )}
    </div>
  );

  // Related Information Tab Content (if available)
  const RelatedInfo = () => (
    <div className="space-y-6">
      {data.booking ? (
        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Booking Information
          </h3>
          <div className="grid sm:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 dark:text-gray-400">
                Booking Reference
              </p>
              <p className="font-medium">{data.booking.bookingRef || 'N/A'}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Package</p>
              <p className="font-medium">
                {data.booking.package?.name || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Booking Date</p>
              <p className="font-medium">
                {data.booking.createdAt
                  ? dayjs(data.booking.createdAt).format('MMMM D, YYYY')
                  : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Booking Status</p>
              <div className="mt-1">
                {data.booking.status &&
                  StatusBadge({
                    status: data.booking.status.toLowerCase(),
                  })}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-6 text-gray-500">
          No related information available for this transaction
        </div>
      )}
    </div>
  );

  // Define tabs
  const tabs: TabProps[] = [
    {
      label: 'Transaction',
      icon: <Receipt className="w-4 h-4" />,
      content: <TransactionInfo />,
    },
    {
      label: 'User',
      icon: <User className="w-4 h-4" />,
      content: <UserInfo />,
    },
    {
      label: 'Related',
      icon: <CreditCard className="w-4 h-4" />,
      content: <RelatedInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Transaction Details"
      description={`Transaction with reference ${data.reference}`}
    >
      <>
        {/* Tabs */}
        <div className="flex border-b mb-4">
          {tabs.map((tab, index) => (
            <button
              key={index}
              className={cn(
                'flex items-center gap-1 px-4 py-2 text-sm font-medium',
                activeTab === index
                  ? 'border-b-2 border-primary text-primary'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              )}
              onClick={() => setActiveTab(index)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab content */}
        <div className="py-2">{tabs[activeTab].content}</div>

        {/* Action buttons */}
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </div>
      </>
    </Modal>
  );
};

export default Details;
