import React, { useState } from 'react';
import {
  InputField,
  CurrencyInputField,
  FormRow,
} from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { MenuFormSchema, MenuValue } from '@/components/validations/inventory';
import { CustomSelectForm } from '@/components/common/another';
import { ModalProps } from '@/components/types';
import { GetAllMenuCat } from '@/api/cafeteria/menu';

const AddMenu: React.FC<ModalProps> = ({ setOpen, mutate, open, profile }) => {
  const { menuCategory } = GetAllMenuCat();
  const [isLoading, setIsLoading] = useState(false);

  const menuCategoryData = menuCategory?.data;

  const form = useForm<MenuValue>({
    resolver: zod<PERSON><PERSON>olver(MenuFormSchema),
    defaultValues: {
      general: '',
      staff: '',
      name: '',
    },
  });

  const onSubmit = async (data: MenuValue) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/menu/add-new', {
        name: data.name,
        category: data.category,
        generalPrice: Number(data.general),
        staffPrice: Number(data.staff),
        createdBy: profile?.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add New Reward"
      description="Create a new reward for the system"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <CustomSelectForm
              control={form.control}
              name="category"
              label="Menu Category"
              placeholder="Select menu category"
              options={menuCategoryData}
            />
            <InputField
              control={form.control}
              name="name"
              label="Menu Name"
              placeholder="Enter inventory name"
              type="text"
            />
            <CurrencyInputField
              control={form.control}
              name="general"
              label="General Price"
              placeholder=""
            />
            <CurrencyInputField
              control={form.control}
              name="staff"
              label="Staff Price"
              placeholder=""
            />
          </FormRow>
        </form>
      </Form>
    </Modal>
  );
};

export default AddMenu;
