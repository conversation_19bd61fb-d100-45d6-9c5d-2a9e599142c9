'use client';

import { notificationStore } from '@/store/notificationStore';

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: string;
  messageId?: string;
}

interface UserPresence {
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: string;
  emoji?: string;
  customMessage?: string;
  clearAfter?: Date;
}

interface TypingIndicator {
  userId: string;
  userName: string;
  groupId?: string;
  recipientId?: string;
  isTyping: boolean;
}

interface MessageDelivery {
  messageId: string;
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
}

class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private url: string;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private isConnected = false;
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor(url: string) {
    this.url = url;
  }

  connect() {
    if (this.socket?.readyState === WebSocket.OPEN) {
      console.log('WebSocket is already connected');
      return;
    }

    try {
      this.socket = new WebSocket(this.url);

      this.socket.onopen = () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.processMessageQueue();
        this.emit('connected', {});
      };

      this.socket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.socket.onclose = (event) => {
        console.log(`WebSocket closed with code ${event.code}`);
        this.isConnected = false;
        this.stopHeartbeat();
        this.emit('disconnected', { code: event.code });
        this.attemptReconnect();
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnected = false;
        this.socket?.close();
      };
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(
      `Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`
    );

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'notification':
        this.handleNotification(message.data);
        break;
      case 'forum_message':
        this.handleForumMessage(message.data);
        break;
      case 'message_delivered':
        this.handleMessageDelivery(message.data);
        break;
      case 'user_presence':
        this.handleUserPresence(message.data);
        break;
      case 'typing_indicator':
        this.handleTypingIndicator(message.data);
        break;
      case 'message_reaction':
        this.handleMessageReaction(message.data);
        break;
      case 'group_update':
        this.handleGroupUpdate(message.data);
        break;
      case 'game_update':
        this.handleGameUpdate(message.data);
        break;
      case 'pong':
        // Heartbeat response
        break;
      default:
        console.log('Unhandled message type:', message.type);
    }

    // Emit event for listeners
    this.emit(message.type, message.data);
  }

  private handleNotification(data: any) {
    notificationStore.addNotification({
      title: data.title,
      message: data.message,
      link: data.link,
    });
  }

  private handleForumMessage(data: any) {
    // Handle forum messages
    notificationStore.addNotification({
      title: 'New Message',
      message: `New message from ${data.sender}: ${data.preview}`,
      link: `/forum/${data.groupId || `direct-messages/${data.senderId}`}`,
    });
  }

  private handleMessageDelivery(data: MessageDelivery) {
    // Emit delivery status for message tracking
    this.emit('message_delivery', data);
  }

  private handleUserPresence(data: UserPresence) {
    // Emit presence update for UI components
    this.emit('user_presence', data);
  }

  private handleTypingIndicator(data: TypingIndicator) {
    // Emit typing indicator for chat components
    this.emit('typing_indicator', data);
  }

  private handleMessageReaction(data: any) {
    // Handle message reactions
    this.emit('message_reaction', data);
  }

  private handleGroupUpdate(data: any) {
    // Handle group updates (member added/removed, settings changed)
    this.emit('group_update', data);
  }

  private handleGameUpdate(data: any) {
    // Handle game-related updates
    this.emit('game_update', data);

    // Show notification for game events
    if (data.type === 'game_won' || data.type === 'prize_awarded') {
      notificationStore.addNotification({
        title: data.title || 'Game Update',
        message: data.message,
        link: data.link || '/forum/games',
      });
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.stopHeartbeat();
    this.isConnected = false;
    this.messageQueue = [];
  }

  send(data: any) {
    const message: WebSocketMessage = {
      ...data,
      timestamp: new Date().toISOString(),
      messageId: data.messageId || this.generateMessageId(),
    };

    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, queuing message');
      this.messageQueue.push(message);
    }
  }

  // Event listener methods
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach((callback) => callback(data));
    }
  }

  // Heartbeat mechanism
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' });
      }
    }, 30000); // Send ping every 30 seconds
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Process queued messages when connection is restored
  private processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.socket?.send(JSON.stringify(message));
      }
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Utility methods for forum features
  joinGroup(groupId: string) {
    this.send({
      type: 'join_group',
      data: { groupId },
    });
  }

  leaveGroup(groupId: string) {
    this.send({
      type: 'leave_group',
      data: { groupId },
    });
  }

  sendTypingIndicator(
    groupId?: string,
    recipientId?: string,
    isTyping: boolean = true
  ) {
    this.send({
      type: 'typing_indicator',
      data: { groupId, recipientId, isTyping },
    });
  }

  updatePresence(status: 'online' | 'offline' | 'away' | 'busy') {
    this.send({
      type: 'update_presence',
      data: { status },
    });
  }

  markMessageAsRead(messageId: string) {
    this.send({
      type: 'mark_read',
      data: { messageId },
    });
  }

  // Game-related methods
  joinGame(gameId: string) {
    this.send({
      type: 'join_game',
      data: { gameId },
    });
  }

  leaveGame(gameId: string) {
    this.send({
      type: 'leave_game',
      data: { gameId },
    });
  }

  sendGameAction(gameId: string, action: string, data: any) {
    this.send({
      type: 'game_action',
      data: { gameId, action, ...data },
    });
  }

  // Connection status
  isConnectedToServer(): boolean {
    return this.isConnected && this.socket?.readyState === WebSocket.OPEN;
  }
}

// Create a singleton instance
// Replace with your actual WebSocket server URL
const websocketService = new WebSocketService('wss://your-backend-url/ws');

export default websocketService;
