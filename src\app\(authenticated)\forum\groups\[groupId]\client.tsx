'use client';

import React from 'react';
import { ForumSidebar } from '@/components/forum/ForumSidebar';
import { GroupChatView } from '@/components/forum/GroupChatView';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface GroupPageClientProps {
  groupId: string;
}

export function GroupPageClient({ groupId }: GroupPageClientProps) {
  const canViewGroups = hasPermission(PERMISSIONS.FORUM_VIEW_GROUP);
  const router = useRouter();

  if (!canViewGroups) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            You don&apos;t have permission to view forum groups.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row h-full">
      <div className="md:hidden p-4 border-b flex justify-between items-center">
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-8 w-8"
          onClick={() => router.push('/forum')}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-xl font-bold">Group Chat</h1>
        <div className="w-8"></div> {/* Empty div for centering */}
      </div>
      <div className="hidden md:block">
        <ForumSidebar />
      </div>
      <GroupChatView groupId={groupId} />
    </div>
  );
}
