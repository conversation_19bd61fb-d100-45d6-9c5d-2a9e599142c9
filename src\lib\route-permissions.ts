'use client';

import { PERMISSIONS } from './types/permissions';
import { Paths } from '@/components/navigations/data';

// Map paths to required permissions
export const pathPermissionMap: Record<string, string> = {
  [Paths.Dashboard]: PERMISSIONS.DASHBOARD_VIEW,
  [Paths.Packages]: PERMISSIONS.PACKAGE_VIEW,
  [Paths.Feedbacks]: PERMISSIONS.FEEDBACK_VIEW,
  [Paths.Referral]: PERMISSIONS.REFERRAL_VIEW,
  [Paths.Reporting]: PERMISSIONS.INCIDENT_VIEW,
  [Paths.Discounts]: PERMISSIONS.REWARD_VIEW,
  [Paths.Transactions]: PERMISSIONS.TRANSACTION_VIEW,
  [Paths.Staffs]: PERMISSIONS.STAFF_VIEW,
  [Paths.Rewards]: PERMISSIONS.REWARD_VIEW,
  [Paths.Location]: PERMISSIONS.LOCATION_EDIT,
  [Paths.Forum]: PERMISSIONS.FORUM_VIEW,
  [Paths.Cafeteria]: PERMISSIONS.CAFETERIA_VIEW,
  [Paths.InnovationHub]: PERMISSIONS.DASHBOARD_VIEW, // Using dashboard view as default permission
  [Paths.ProcessDictionary]: PERMISSIONS.DASHBOARD_VIEW, // Using dashboard view as default permission
};

// Helper function to find the first accessible route
// export function findFirstAccessibleRoute(
//   hasPermissionFn: (permission: string) => boolean
// ): string | undefined {
//   return Object.entries(pathPermissionMap).find(
//     ([_, permission]) => hasPermissionFn(permission)
//   )?.[0];
// }

export const permissionToRoute: Record<string, string> = Object.entries(
  pathPermissionMap
).reduce(
  (acc, [path, perm]) => {
    if (!acc[perm]) {
      acc[perm] = path;
    }
    return acc;
  },
  {} as Record<string, string>
);

export const findFirstAccessibleRoute = (
  permissions: string[]
): string | null => {
  for (const [perm, route] of Object.entries(permissionToRoute)) {
    if (permissions.includes(perm)) return route;
  }
  return null;
};
