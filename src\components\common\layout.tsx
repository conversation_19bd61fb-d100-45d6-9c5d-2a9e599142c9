'use client';

import type { ReactNode } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTheme } from 'next-themes';
import { SWRConfig } from 'swr';
import { toast } from 'sonner';

import Sidebar from '../navigations/sideBar';
import TopNav from './top-nav';
import { fetcher } from '@/api/fetcher';
import { NetworkError } from './network-error';
import { useProfileVerification } from '@/hooks/useProfileVerification';
import useIsLoggedIn from '@/hooks/isLoggedIn';
import { Logout } from '@/lib/utils';
import { pathPermissionMap, permissionToRoute } from '@/lib/route-permissions';

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const { theme } = useTheme();
  const isLoggedIn = useIsLoggedIn();
  const router = useRouter();
  const pathname = usePathname();

  const [mounted, setMounted] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  const {
    isLoading: loadingProfile,
    isSuccess: profileVerified,
    isError: profileError,
    isTimeout: profileTimeout,
    error: profileErrorDetails,
    permissions = [],
  } = useProfileVerification(20000);

  const normalizedPath =
    pathname.endsWith('/') && pathname !== '/'
      ? pathname.slice(0, -1)
      : pathname;

  const hasHandledRedirect = useRef(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    setRedirecting(false);
    hasHandledRedirect.current = false;
  }, [pathname]);

  // Redirect user if current route not permitted
  useEffect(() => {
    if (
      !mounted ||
      !isLoggedIn ||
      loadingProfile ||
      !profileVerified ||
      redirecting ||
      hasHandledRedirect.current ||
      permissions.length === 0
    ) {
      return;
    }

    const requiredPermission = pathPermissionMap[normalizedPath];

    if (!requiredPermission || permissions.includes(requiredPermission)) {
      return; // user is allowed
    }

    const fallbackRoute = permissions
      .map((perm: string | number) => permissionToRoute[perm])
      .find(Boolean);

    hasHandledRedirect.current = true;

    if (fallbackRoute && fallbackRoute !== normalizedPath) {
      setRedirecting(true);
      router.replace(fallbackRoute);
    } else {
      toast.error('You no longer have access. Logging out...');
      Logout();
    }
  }, [
    mounted,
    isLoggedIn,
    loadingProfile,
    profileVerified,
    permissions,
    normalizedPath,
    redirecting,
    router,
  ]);

  // Handle profile verification errors
  useEffect(() => {
    if (
      isLoggedIn &&
      !loadingProfile &&
      profileError &&
      !profileTimeout &&
      pathname !== '/forbidden'
    ) {
      console.error('Profile verification failed:', profileErrorDetails);
      router.replace('/forbidden');
    }
  }, [
    isLoggedIn,
    loadingProfile,
    profileError,
    profileTimeout,
    pathname,
    profileErrorDetails,
    router,
  ]);

  if (profileTimeout) {
    return (
      <NetworkError
        isTimeout
        error={profileErrorDetails as Error}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (
    !mounted ||
    isLoggedIn === null ||
    loadingProfile ||
    redirecting ||
    (isLoggedIn && profileVerified && !permissions.length)
  ) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-primary border-t-2 border-b-2" />
          <p className="text-sm text-muted-foreground">
            Verifying permissions...
          </p>
        </div>
      </div>
    );
  }

  return (
    <SWRConfig
      value={{ fetcher, revalidateOnFocus: true, revalidateOnReconnect: true }}
    >
      <div
        className={`mx-auto flex h-screen ${theme === 'dark' ? 'dark' : ''}`}
      >
        <Sidebar />
        <div className="w-full flex flex-col">
          <header className="h-16 border-b border-gray-200 dark:border-[#1F1F23]">
            <TopNav />
          </header>
          <main className="flex-1 overflow-y-scroll p-6 bg-white dark:bg-[#0F0F12]">
            {children}
          </main>
        </div>
      </div>
    </SWRConfig>
  );
}
