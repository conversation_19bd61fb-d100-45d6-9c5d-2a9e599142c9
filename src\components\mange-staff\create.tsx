import React, { useState } from 'react';
import { InputField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../types';
import { CustomSelectForm } from '@/components/common/another';
import { GetRewards, GetDepartmentList } from '@/api/reward/data';
import { GetSpecialty } from '@/api/staff';
import { MultiSelect } from '@/components/common/multi-select';
import { Label } from '@/components/ui/label';
import { Check } from 'lucide-react';
import { GetLocations } from '@/api/data';
import { Checkbox } from '@/components/ui/checkbox';
import {
  StaffRegFormSchema,
  StaffFormValues,
} from '@/components/validations/reward';

interface Reward {
  id: string | number;
  name: string;
}

const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRewards, setSelectedRewards] = useState<string[]>([]);
  const [isDoctor, setIsDoctor] = useState(false);
  const [isConsultant, setIsConsultant] = useState(false);
  const [isVisitingConsultant, setIsVisitingConsultant] = useState(false);

  const { department, departmentLoading } = GetDepartmentList('');
  const { rewards } = GetRewards(`deactivated=false`);
  const { specialty } = GetSpecialty();
  const { locations } = GetLocations();

  const rewardData = rewards?.data;
  const specialtyData = specialty?.data;
  const locationData = locations?.data;
  const departmentData = department?.data;

  const converted = locationData?.map(({ name }: { name: string }) => ({
    id: name,
    name,
  }));

  //   const convertedDeptData = departmentData?.map(({ name }: { name: string }) => ({
  //   id: name,
  //   name,
  // }));

  const staffType = [
    {
      id: 'FULL',
      name: 'Full',
    },
    {
      id: 'CONTRACT',
      name: 'Contract',
    },
    {
      id: 'INTERN',
      name: 'Intern',
    },
  ];

  const form = useForm<StaffFormValues>({
    resolver: zodResolver(StaffRegFormSchema),
    defaultValues: {
      name: '',
      staffId: '',
      phone: '',
      email: '',
      role: '',
    },
  });

  const onSubmit = async (data: StaffFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/staff/add-new', {
        fullName: data.name,
        staffCode: data.staffId,
        locationId: data.location,
        email: data.email,
        rewardIds: selectedRewards,
        phoneNumber: data.phone,
        type: data.type,
        role: data.role,
        departmentId: data.department,
        isDoctor: isDoctor,
        isConsultant: isConsultant,
        isVisitingConsultant: isVisitingConsultant,
        specialtyId: data.specialty,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
        setIsDoctor(false);
        setIsConsultant(false);
        setIsVisitingConsultant(false);
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add Staff"
      description="Adding a new staff generates a referral code for the staff"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-2">
          <FormRow>
            <CustomSelectForm
              control={form.control}
              name="type"
              label="Staff Type"
              placeholder="Select the staff type"
              options={staffType}
            />
            <CustomSelectForm
              control={form.control}
              name="location"
              label="Location"
              placeholder="Select staff location"
              options={locationData || []}
            />
            <CustomSelectForm
              control={form.control}
              name="department"
              label="Department"
              placeholder="Select staff department"
              options={departmentData || []}
            />

            <InputField
              control={form.control}
              name="role"
              label="Role"
              placeholder="Enter staff role"
              type="text"
            />
            <InputField
              control={form.control}
              name="phone"
              label="Phone Number"
              placeholder="Enter staff phone number"
              type="text"
            />
            <InputField
              control={form.control}
              name="staffId"
              label="Staff Id"
              placeholder="Enter staff Id number"
              type="text"
            />
          </FormRow>
          <div className="space-y-2">
            <Label htmlFor="prices">Select applicable rewards</Label>
            <MultiSelect
              className="overflow-auto"
              options={rewardData || []}
              selected={selectedRewards}
              onChange={setSelectedRewards}
              placeholder="Select applicable rewards"
              valueField="id"
              labelField="name"
              badgeClassName="bg-primary text-primary-foreground hover:bg-primary/90"
              renderOption={(reward, isSelected) => (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <div className="mr-2 flex h-4 w-4 items-center justify-center">
                      {isSelected ? <Check className="h-4 w-4" /> : null}
                    </div>
                    <span>{(reward as unknown as Reward).name}</span>
                  </div>
                </div>
              )}
            />
            {/* {selectedRewards.length === 0 && form.formState.isSubmitted && (
              <p className="text-sm text-red-500 mt-1">
                Select at least one reward
              </p>
            )} */}
          </div>
          <InputField
            control={form.control}
            name="name"
            label="Full Name"
            placeholder="Provide staff full name"
            type="text"
          />
          <InputField
            control={form.control}
            name="email"
            label="Email Address"
            placeholder="Enter a valid email address"
            type="text"
          />

          <div className="flex items-center space-x-6 my-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isDoctor"
                checked={isDoctor}
                onCheckedChange={(checked) => {
                  setIsDoctor(checked === true);
                  if (!checked) {
                    setIsConsultant(false);
                    setIsVisitingConsultant(false);
                  }
                }}
              />
              <Label htmlFor="isDoctor">Is Doctor</Label>
            </div>

            {isDoctor && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isConsultant"
                  checked={isConsultant}
                  onCheckedChange={(checked) => {
                    setIsConsultant(checked === true);
                    if (!checked) {
                      setIsVisitingConsultant(false);
                    }
                  }}
                />
                <Label htmlFor="isConsultant">Is Consultant</Label>
              </div>
            )}

            {isDoctor && isConsultant && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isVisitingConsultant"
                  checked={isVisitingConsultant}
                  onCheckedChange={(checked) =>
                    setIsVisitingConsultant(checked === true)
                  }
                />
                <Label htmlFor="isVisitingConsultant">
                  Is Visiting Consultant
                </Label>
              </div>
            )}
          </div>
          <div className="my-4">
            {isDoctor && isConsultant && (
              <CustomSelectForm
                control={form.control}
                name="specialty"
                label="Specialty"
                placeholder="Select specialty"
                options={specialtyData || []}
              />
            )}
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
