'use client';

import React from 'react';
import { LogOut, User, KeyRound, UserCog, Settings } from 'lucide-react';
import { Button } from '../ui/button';
import { useRouter } from 'next/navigation';
import { UserProps } from './types';
import { Logout } from '@/lib/utils';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

interface ProfileProps {
  user: UserProps;
  closeDropdown?: () => void;
  onViewProfile?: () => void;
  onChangePassword?: () => void;
  onManageStaffs?: () => void;
  onSystemSettings?: () => void;
}

export default function Profile({
  user,
  closeDropdown: externalCloseDropdown,
  onViewProfile,
  onChangePassword,
  onManageStaffs,
  onSystemSettings,
}: ProfileProps) {
  const router = useRouter();

  // Function to close the dropdown - use external if provided, otherwise fallback to DOM method
  const closeDropdown = () => {
    if (externalCloseDropdown) {
      externalCloseDropdown();
    } else {
      // Fallback method - find the closest dropdown element and close it
      const dropdownTrigger = document.querySelector(
        '[data-state="open"][data-slot="dropdown-menu-trigger"]'
      );
      if (dropdownTrigger) {
        (dropdownTrigger as HTMLElement).click();
      }
    }
  };

  const handleLogOut = async () => {
    Logout();
    router.push('/login');
  };

  const handleViewProfile = () => {
    // Use the handler passed from parent if available
    if (onViewProfile) {
      onViewProfile();
    } else {
      // Just close the dropdown if no handler provided
      closeDropdown();
      console.warn('No view profile handler provided');
    }
  };

  const handleChangePassword = () => {
    // Use the handler passed from parent if available
    if (onChangePassword) {
      onChangePassword();
    } else {
      // Just close the dropdown if no handler provided
      closeDropdown();
      console.warn('No change password handler provided');
    }
  };

  const handleManageStaffs = () => {
    // Use the handler passed from parent if available
    if (onManageStaffs) {
      onManageStaffs();
    } else {
      // First close the dropdown
      closeDropdown();
      // Navigate to manage admins page
      router.push('/manage-staff');
    }
  };

  const handleSystemSettings = () => {
    // Use the handler passed from parent if available
    if (onSystemSettings) {
      onSystemSettings();
    } else {
      // First close the dropdown
      closeDropdown();
      // Navigate to system settings page
      router.push('/system-settings');
    }
  };

  // If user is not defined, show a loading state
  if (!user) {
    return (
      <div className="w-full max-w-sm mx-auto p-4 text-center">
        <div className="animate-pulse">
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto mb-2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="w-full max-w-sm mx-auto">
        <div className="relative overflow-hidden rounded-2xl border border-zinc-200 dark:border-zinc-800">
          <div className="relative p-4">
            <div className="flex items-center gap-4">
              {/* Profile Info */}
              <div className="flex-1">
                <h2 className="text-sm font-semibold text-zinc-900 dark:text-zinc-100">
                  {user.fullName || 'User'}
                </h2>
                {/* <p className="text-xs font-medium text-zinc-600 dark:text-zinc-400">
                  {user.role?.name || 'Loading...'}
                </p> */}
              </div>
            </div>
            <div className="h-px bg-zinc-200 dark:bg-zinc-800 my-4" />
            <div className="space-y-2">
              {/* View Profile */}
              <Button
                variant="ghost"
                className="flex w-full items-center justify-start p-2
                  hover:bg-zinc-200 dark:hover:bg-zinc-800/50
                  rounded-lg transition-colors duration-200"
                onClick={handleViewProfile}
              >
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                    View Profile
                  </span>
                </div>
              </Button>

              {/* Manage Admins - Show if user has staff:read permission */}
              {hasPermission(PERMISSIONS.STAFF_VIEW) && (
                <Button
                  variant="ghost"
                  className="flex w-full items-center justify-start p-2
                    hover:bg-zinc-200 dark:hover:bg-zinc-800/50
                    rounded-lg transition-colors duration-200"
                  onClick={handleManageStaffs}
                >
                  <div className="flex items-center gap-2">
                    <UserCog className="w-4 h-4" />
                    <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                      Manage Staffs
                    </span>
                  </div>
                </Button>
              )}

              {/* System Settings - Show if user has settings:read permission */}
              {hasPermission(PERMISSIONS.SETTINGS_VIEW) && (
                <Button
                  variant="ghost"
                  className="flex w-full items-center justify-start p-2
                    hover:bg-zinc-200 dark:hover:bg-zinc-800/50
                    rounded-lg transition-colors duration-200"
                  onClick={handleSystemSettings}
                >
                  <div className="flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                      System Settings
                    </span>
                  </div>
                </Button>
              )}

              {/* Change Password */}
              <Button
                variant="ghost"
                className="flex w-full items-center justify-start p-2
                  hover:bg-zinc-200 dark:hover:bg-zinc-800/50
                  rounded-lg transition-colors duration-200"
                onClick={handleChangePassword}
              >
                <div className="flex items-center gap-2">
                  <KeyRound className="w-4 h-4" />
                  <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                    Change Password
                  </span>
                </div>
              </Button>

              {/* Logout - No DropdownMenuClose here as we want to keep the dropdown open during logout */}
              <Button
                type="button"
                onClick={handleLogOut}
                variant="outline"
                className="w-full border-red-500 cursor-pointer"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
