'use client';

import { useState, useEffect } from 'react';
import { useSnapshot } from 'valtio';
import { Bell } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { notificationStore } from '@/store/notificationStore';
import Link from 'next/link';

export function NotificationBell() {
  const { notifications, markAsRead, markAllAsRead } =
    useSnapshot(notificationStore);
  const [open, setOpen] = useState(false);
  const [pulse, setPulse] = useState(false);

  const unreadCount = notifications.filter((n) => !n.read).length;

  // Pulse animation when new notifications arrive
  useEffect(() => {
    if (unreadCount > 0) {
      setPulse(true);
      const timer = setTimeout(() => setPulse(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [unreadCount]);

  const handleNotificationClick = (id: string) => {
    markAsRead(id);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={`relative ${pulse ? 'animate-pulse' : ''}`}
        >
          <Bell
            className={`h-5 w-5 ${unreadCount > 0 ? 'text-primary' : ''}`}
          />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-5 w-5 rounded-full bg-red-500 text-[10px] flex items-center justify-center text-white font-bold shadow-sm">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h4 className="font-medium">Notifications</h4>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs"
              onClick={() => markAllAsRead()}
            >
              Mark all as read
            </Button>
          )}
        </div>
        <div className="max-h-80 overflow-auto">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              No notifications
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-b last:border-0 cursor-pointer hover:bg-muted/50 ${
                  notification.read ? 'bg-background' : 'bg-muted/30'
                }`}
                onClick={() => handleNotificationClick(notification.id)}
              >
                {!notification.read && (
                  <div className="w-2 h-2 rounded-full bg-primary float-right mt-1"></div>
                )}
                {notification.link ? (
                  <Link href={notification.link} className="block">
                    <h5 className="font-medium">{notification.title}</h5>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {format(
                        new Date(notification.timestamp),
                        'MMM d, h:mm a'
                      )}
                    </p>
                  </Link>
                ) : (
                  <>
                    <h5 className="font-medium">{notification.title}</h5>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {format(
                        new Date(notification.timestamp),
                        'MMM d, h:mm a'
                      )}
                    </p>
                  </>
                )}
              </div>
            ))
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
