import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import {
  Menu,
  ArrowLeft,
  Users,
  MessageCircle,
  Trophy,
  Search,
  Plus,
  MoreVertical,
  Phone,
  Video,
  Info,
} from 'lucide-react';
import { ForumSidebar } from './ForumSidebar';
import { useRouter } from 'next/navigation';
import { useForum, useUnreadCounts } from '@/hooks/useForum';
import { cn } from '@/lib/utils';

interface MobileForumLayoutProps {
  children: React.ReactNode;
  currentView: 'groups' | 'direct-messages' | 'games' | 'chat';
  currentTitle?: string;
  showBackButton?: boolean;
  onBack?: () => void;
  rightActions?: React.ReactNode;
  className?: string;
}

export const MobileForumLayout: React.FC<MobileForumLayoutProps> = ({
  children,
  currentView,
  currentTitle,
  showBackButton = false,
  onBack,
  rightActions,
  className,
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const router = useRouter();

  const { activeGroupId, activeRecipientId } = useForum();
  const { totalUnread } = useUnreadCounts();

  // Close sidebar when route changes
  useEffect(() => {
    setIsSidebarOpen(false);
  }, [currentView, activeGroupId, activeRecipientId]);

  const getViewTitle = () => {
    if (currentTitle) return currentTitle;

    switch (currentView) {
      case 'groups':
        return 'Groups';
      case 'direct-messages':
        return 'Direct Messages';
      case 'games':
        return 'Games & Surveys';
      case 'chat':
        return 'Chat';
      default:
        return 'Forum';
    }
  };

  const getViewIcon = () => {
    switch (currentView) {
      case 'groups':
        return <Users className="h-5 w-5" />;
      case 'direct-messages':
        return <MessageCircle className="h-5 w-5" />;
      case 'games':
        return <Trophy className="h-5 w-5" />;
      case 'chat':
        return <MessageCircle className="h-5 w-5" />;
      default:
        return <MessageCircle className="h-5 w-5" />;
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <div className={cn('flex flex-col h-screen bg-background', className)}>
      {/* Mobile Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="flex items-center gap-3">
          {showBackButton ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
          ) : (
            <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2 relative">
                  <Menu className="h-5 w-5" />
                  {totalUnread > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
                    >
                      {totalUnread > 99 ? '99+' : totalUnread}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0 w-80">
                <ForumSidebar />
              </SheetContent>
            </Sheet>
          )}

          <div className="flex items-center gap-2">
            {getViewIcon()}
            <h1 className="font-semibold text-lg truncate">{getViewTitle()}</h1>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Search Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="p-2"
          >
            <Search className="h-5 w-5" />
          </Button>

          {/* Custom Right Actions */}
          {rightActions}

          {/* Default Actions for Chat View */}
          {currentView === 'chat' && !rightActions && (
            <>
              <Button variant="ghost" size="sm" className="p-2">
                <Phone className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="sm" className="p-2">
                <Video className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="sm" className="p-2">
                <Info className="h-5 w-5" />
              </Button>
            </>
          )}

          {/* More Options */}
          <Button variant="ghost" size="sm" className="p-2">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Search Bar (Expandable) */}
      {isSearchOpen && (
        <div className="p-4 border-b bg-muted/50">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search messages, users, or groups..."
              className="w-full pl-10 pr-4 py-2 bg-background border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              autoFocus
            />
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">{children}</div>

      {/* Mobile Bottom Navigation (Optional) */}
      {!showBackButton && (
        <div className="flex items-center justify-around p-2 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:hidden">
          <Button
            variant={currentView === 'groups' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => router.push('/forum')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <Users className="h-4 w-4" />
            <span className="text-xs">Groups</span>
          </Button>

          <Button
            variant={currentView === 'direct-messages' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => router.push('/forum/direct-messages')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2 relative"
          >
            <MessageCircle className="h-4 w-4" />
            <span className="text-xs">DMs</span>
            {totalUnread > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
              >
                {totalUnread > 9 ? '9+' : totalUnread}
              </Badge>
            )}
          </Button>

          <Button
            variant={currentView === 'games' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => router.push('/forum/games')}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <Trophy className="h-4 w-4" />
            <span className="text-xs">Games</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              /* Handle create action */
            }}
            className="flex-1 flex flex-col items-center gap-1 h-auto py-2"
          >
            <Plus className="h-4 w-4" />
            <span className="text-xs">Create</span>
          </Button>
        </div>
      )}
    </div>
  );
};

// Hook for detecting mobile view
export const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
};

// Responsive wrapper component
interface ResponsiveForumLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  mobileProps?: Partial<MobileForumLayoutProps>;
  className?: string;
}

export const ResponsiveForumLayout: React.FC<ResponsiveForumLayoutProps> = ({
  children,
  sidebar,
  mobileProps = {},
  className,
}) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <MobileForumLayout
        currentView="groups"
        {...mobileProps}
        className={className}
      >
        {children}
      </MobileForumLayout>
    );
  }

  return (
    <div className={cn('flex h-screen', className)}>
      {sidebar && <div className="w-80 border-r bg-muted/50">{sidebar}</div>}
      <div className="flex-1 overflow-hidden">{children}</div>
    </div>
  );
};
