import useSWR, { mutate } from 'swr';

export const GetAllReferrals = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/referral/list-all-referral?${qs.toString()}`
  );

  return {
    referrals: data,
    referralLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllReferringEntites = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/referral/list-referring-entity?${qs.toString()}`
  );

  return {
    referrers: data,
    referrerLoading: isLoading,
    mutate: mutate,
  };
};
