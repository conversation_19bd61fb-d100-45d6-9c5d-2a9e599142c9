import { cn } from '@/lib/utils';

type Status =
  | 'completed'
  | 'processing'
  | 'pending'
  | 'cancelled'
  | 'draft'
  | 'active'
  | 'confirmed'
  | 'inactive';

interface StatusBadgeProps {
  status: Status | string;
  onClick?: () => void;
  className?: string;
}

export function StatusBadge({ status, onClick, className }: StatusBadgeProps) {
  return (
    <span
      onClick={onClick}
      className={cn(
        'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold',
        className,
        (status === 'completed' ||
          status === 'active' ||
          status === 'confirmed') &&
          'bg-green-100 text-green-800',
        status === 'processing' && 'bg-blue-100 text-blue-800',
        status === 'pending' && 'bg-yellow-100 text-yellow-800',
        status === 'cancelled' ||
          (status === 'inactive' && 'bg-red-100 text-red-800'),
        status === 'draft' && 'bg-gray-200 text-gray-800'
      )}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}
