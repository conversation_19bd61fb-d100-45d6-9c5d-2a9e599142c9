import React, { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Settings, UserPlus, UserMinus } from 'lucide-react';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { GetForumGroup, GetGroupMessages } from '@/api/forum/data';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { AddMembersModal } from './modals/AddMembersModal';
import { RemoveMembersModal } from './modals/RemoveMembersModal';
import { EditGroupModal } from './modals/EditGroupModal';

interface GroupChatViewProps {
  groupId: string;
}

export const GroupChatView: React.FC<GroupChatViewProps> = ({ groupId }) => {
  const {
    group,
    isLoading: isGroupLoading,
    mutate: mutateGroup,
  } = GetForumGroup(groupId);
  const {
    messages,
    isLoading: isMessagesLoading,
    mutate: mutateMessages,
  } = GetGroupMessages(groupId);

  const [isAddMembersModalOpen, setIsAddMembersModalOpen] =
    React.useState(false);
  const [isRemoveMembersModalOpen, setIsRemoveMembersModalOpen] =
    React.useState(false);
  const [isEditGroupModalOpen, setIsEditGroupModalOpen] = React.useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const canAddMembers = hasPermission(PERMISSIONS.FORUM_ADD_MEMBERS);
  const canRemoveMembers = hasPermission(PERMISSIONS.FORUM_REMOVE_MEMBERS);
  const canEditGroup = hasPermission(PERMISSIONS.FORUM_EDIT_GROUP);
  const canSendMessage = hasPermission(PERMISSIONS.FORUM_SEND_MESSAGE);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleMessageUpdated = () => {
    mutateMessages();
  };

  const handleMessageSent = () => {
    mutateMessages();
  };

  const pinnedMessages = messages?.filter((msg: any) => msg.isPinned) || [];
  const regularMessages = messages?.filter((msg: any) => !msg.isPinned) || [];

  if (isGroupLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        Loading group...
      </div>
    );
  }

  if (!group) {
    return (
      <div className="flex-1 flex items-center justify-center">
        Group not found
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      <div className="border-b p-3 md:p-4 flex justify-between items-center">
        <div className="overflow-hidden">
          <h2 className="text-lg md:text-xl font-semibold truncate">
            {group.name}
          </h2>
          <p className="text-xs md:text-sm text-muted-foreground">
            {group.members?.length || 0} members
          </p>
        </div>

        <div className="flex gap-1 md:gap-2 shrink-0">
          {canAddMembers && (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 md:h-9 md:w-9"
              onClick={() => setIsAddMembersModalOpen(true)}
            >
              <UserPlus className="h-3 w-3 md:h-4 md:w-4" />
            </Button>
          )}

          {canRemoveMembers && (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 md:h-9 md:w-9"
              onClick={() => setIsRemoveMembersModalOpen(true)}
            >
              <UserMinus className="h-3 w-3 md:h-4 md:w-4" />
            </Button>
          )}

          {canEditGroup && (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 md:h-9 md:w-9"
              onClick={() => setIsEditGroupModalOpen(true)}
            >
              <Settings className="h-3 w-3 md:h-4 md:w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        {pinnedMessages.length > 0 && (
          <div className="mb-4">
            <div className="text-sm font-medium text-muted-foreground mb-2">
              Pinned Messages
            </div>
            {pinnedMessages.map((message: any) => (
              <ChatMessage
                key={message.id}
                message={message}
                onMessageUpdated={handleMessageUpdated}
              />
            ))}
            <div className="border-t my-4"></div>
          </div>
        )}

        {isMessagesLoading ? (
          <div className="flex justify-center p-4">Loading messages...</div>
        ) : regularMessages.length > 0 ? (
          regularMessages.map((message: any) => (
            <ChatMessage
              key={message.id}
              message={message}
              onMessageUpdated={handleMessageUpdated}
            />
          ))
        ) : (
          <div className="text-center text-muted-foreground p-4">
            No messages yet. Start the conversation!
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {canSendMessage && (
        <ChatInput groupId={groupId} onMessageSent={handleMessageSent} />
      )}

      {isAddMembersModalOpen && (
        <AddMembersModal
          isOpen={isAddMembersModalOpen}
          onClose={() => setIsAddMembersModalOpen(false)}
          groupId={groupId}
          currentMembers={group.members || []}
          onMembersAdded={() => mutateGroup()}
        />
      )}

      {isRemoveMembersModalOpen && (
        <RemoveMembersModal
          isOpen={isRemoveMembersModalOpen}
          onClose={() => setIsRemoveMembersModalOpen(false)}
          groupId={groupId}
          currentMembers={group.members || []}
          onMembersRemoved={() => mutateGroup()}
        />
      )}

      {isEditGroupModalOpen && (
        <EditGroupModal
          isOpen={isEditGroupModalOpen}
          onClose={() => setIsEditGroupModalOpen(false)}
          group={group}
          onGroupUpdated={() => mutateGroup()}
        />
      )}
    </div>
  );
};
