import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MessageCircle,
  MoreHorizontal,
  Reply,
  Edit,
  Trash2,
  Pin,
  Copy,
  Flag,
  Smile,
  FileText,
  Image as ImageIcon,
  Download,
} from 'lucide-react';
import { ForumMessage } from '@/api/forum/data';
import { useMessageReactions } from '@/hooks/useForum';
import { addMessageReaction, removeMessageReaction } from '@/api/forum/data';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface MessageItemProps {
  message: ForumMessage;
  isOwn: boolean;
  showAvatar?: boolean;
  onReply?: (message: ForumMessage) => void;
  onEdit?: (message: ForumMessage) => void;
  onDelete?: (messageId: string) => void;
  onPin?: (messageId: string) => void;
  className?: string;
}

const COMMON_EMOJIS = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🔥'];

export const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isOwn,
  showAvatar = true,
  onReply,
  onEdit,
  onDelete,
  onPin,
  className,
}) => {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const reactions = useMessageReactions(message.id);

  const handleReaction = async (emoji: string) => {
    try {
      const existingReaction = reactions.find(
        (r: any) => r.emoji === emoji && r.userId === 'current-user-id'
      );

      if (existingReaction) {
        await removeMessageReaction(message.id, emoji);
      } else {
        await addMessageReaction(message.id, emoji);
      }
    } catch (error) {
      console.error('Error handling reaction:', error);
    }
  };

  const copyMessage = () => {
    navigator.clipboard.writeText(message.content);
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const groupedReactions = reactions.reduce(
    (acc: any, reaction: any) => {
      if (!acc[reaction.emoji]) {
        acc[reaction.emoji] = [];
      }
      acc[reaction.emoji].push(reaction);
      return acc;
    },
    {} as Record<string, typeof reactions>
  );

  return (
    <div
      className={cn(
        'group relative p-3 hover:bg-muted/50 transition-colors',
        message.isPinned &&
          'bg-yellow-50 dark:bg-yellow-950/20 border-l-4 border-l-yellow-500',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex gap-3">
        {showAvatar && (
          <Avatar className="h-8 w-8">
            <AvatarImage src={message.senderAvatar} />
            <AvatarFallback>
              {message.senderName.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        )}

        <div className="flex-1 min-w-0">
          {/* Message Header */}
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{message.senderName}</span>
            <span className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(message.createdAt), {
                addSuffix: true,
              })}
            </span>
            {message.isEdited && (
              <Badge variant="secondary" className="text-xs">
                edited
              </Badge>
            )}
            {message.isPinned && <Pin className="h-3 w-3 text-yellow-600" />}
          </div>

          {/* Message Content */}
          <div className="text-sm leading-relaxed mb-2">
            {message.parentMessageId && (
              <div className="mb-2 p-2 bg-muted/50 rounded border-l-2 border-l-primary/50">
                <div className="text-xs text-muted-foreground mb-1">
                  Replying to message
                </div>
                <div className="text-xs truncate">
                  {/* This would show the parent message content */}
                  Original message content...
                </div>
              </div>
            )}

            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>

            {/* Mentions */}
            {message.mentions.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {message.mentions.map((userId) => (
                  <Badge key={userId} variant="outline" className="text-xs">
                    @{userId}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Attachments */}
          {message.attachments.length > 0 && (
            <div className="mb-2 space-y-2">
              {message.attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center gap-2 p-2 bg-muted/50 rounded border"
                >
                  {getFileIcon(attachment.type)}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {attachment.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(attachment.size / 1024).toFixed(1)} KB
                    </div>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Reactions */}
          {Object.keys(groupedReactions).length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {Object.entries(groupedReactions).map(
                ([emoji, reactionList]: [string, any]) => (
                  <Button
                    key={emoji}
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => handleReaction(emoji)}
                  >
                    <span className="mr-1">{emoji}</span>
                    <span>{reactionList.length}</span>
                  </Button>
                )
              )}
            </div>
          )}

          {/* Thread Replies */}
          {message.threadReplies && message.threadReplies > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-primary hover:text-primary/80"
            >
              <MessageCircle className="h-3 w-3 mr-1" />
              {message.threadReplies}{' '}
              {message.threadReplies === 1 ? 'reply' : 'replies'}
            </Button>
          )}
        </div>

        {/* Message Actions */}
        {(isHovered || showEmojiPicker) && (
          <div className="flex items-center gap-1">
            {/* Quick Reactions */}
            <div className="flex items-center gap-1 mr-2">
              {COMMON_EMOJIS.slice(0, 3).map((emoji) => (
                <Button
                  key={emoji}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-muted"
                  onClick={() => handleReaction(emoji)}
                >
                  {emoji}
                </Button>
              ))}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-muted"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              >
                <Smile className="h-3 w-3" />
              </Button>
            </div>

            {/* More Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onReply && (
                  <DropdownMenuItem onClick={() => onReply(message)}>
                    <Reply className="h-4 w-4 mr-2" />
                    Reply
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={copyMessage}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Message
                </DropdownMenuItem>
                {isOwn && onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(message)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onPin && (
                  <DropdownMenuItem onClick={() => onPin(message.id)}>
                    <Pin className="h-4 w-4 mr-2" />
                    {message.isPinned ? 'Unpin' : 'Pin'}
                  </DropdownMenuItem>
                )}
                {!isOwn && (
                  <DropdownMenuItem>
                    <Flag className="h-4 w-4 mr-2" />
                    Report
                  </DropdownMenuItem>
                )}
                {isOwn && onDelete && (
                  <DropdownMenuItem
                    onClick={() => onDelete(message.id)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute top-full right-4 z-10 bg-background border rounded-md shadow-lg p-2 mt-1">
          <div className="grid grid-cols-4 gap-1">
            {COMMON_EMOJIS.map((emoji) => (
              <Button
                key={emoji}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => {
                  handleReaction(emoji);
                  setShowEmojiPicker(false);
                }}
              >
                {emoji}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
