import * as z from 'zod';

export const formSchema = z.object({
  fullName: z
    .string()
    .min(3, { message: 'Name must be at least 3 characters' })
    .max(50, { message: 'Name must be less than 50 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  designation: z.string().min(3, { message: 'Designation cannot be empty' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .max(50, { message: 'Password must be less than 50 characters' }),
  roleId: z.string({ required_error: 'Please select a role' }),
});

export type AdminFormValues = z.infer<typeof formSchema>;
