import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useUserPresence } from '@/hooks/useForum';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface UserPresenceIndicatorProps {
  userId: string;
  userName: string;
  userAvatar?: string;
  showName?: boolean;
  showStatus?: boolean;
  showLastSeen?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const UserPresenceIndicator: React.FC<UserPresenceIndicatorProps> = ({
  userId,
  userName,
  userAvatar,
  showName = true,
  showStatus = true,
  showLastSeen = false,
  size = 'md',
  className,
}) => {
  const presence = useUserPresence(userId);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500 border-green-500';
      case 'away':
        return 'bg-yellow-500 border-yellow-500';
      case 'busy':
        return 'bg-red-500 border-red-500';
      case 'offline':
      default:
        return 'bg-gray-400 border-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      case 'offline':
      default:
        return 'Offline';
    }
  };

  const getAvatarSize = () => {
    switch (size) {
      case 'sm':
        return 'h-6 w-6';
      case 'lg':
        return 'h-12 w-12';
      case 'md':
      default:
        return 'h-8 w-8';
    }
  };

  const getIndicatorSize = () => {
    switch (size) {
      case 'sm':
        return 'h-2 w-2';
      case 'lg':
        return 'h-4 w-4';
      case 'md':
      default:
        return 'h-3 w-3';
    }
  };

  const getIndicatorPosition = () => {
    switch (size) {
      case 'sm':
        return '-bottom-0.5 -right-0.5';
      case 'lg':
        return '-bottom-1 -right-1';
      case 'md':
      default:
        return '-bottom-0.5 -right-0.5';
    }
  };

  const formatLastSeen = (lastSeen: string) => {
    try {
      return formatDistanceToNow(new Date(lastSeen), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn('flex items-center gap-2', className)}>
            <div className="relative">
              <Avatar className={getAvatarSize()}>
                <AvatarImage src={userAvatar} alt={userName} />
                <AvatarFallback
                  className={
                    size === 'sm'
                      ? 'text-xs'
                      : size === 'lg'
                        ? 'text-base'
                        : 'text-sm'
                  }
                >
                  {userName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {showStatus && (
                <div
                  className={cn(
                    'absolute rounded-full border-2 border-background',
                    getIndicatorSize(),
                    getIndicatorPosition(),
                    getStatusColor(presence?.status || 'offline')
                  )}
                />
              )}
            </div>

            {showName && (
              <div className="flex-1 min-w-0">
                <div
                  className={cn(
                    'font-medium truncate',
                    size === 'sm'
                      ? 'text-sm'
                      : size === 'lg'
                        ? 'text-base'
                        : 'text-sm'
                  )}
                >
                  {userName}
                </div>

                {showStatus && (
                  <div
                    className={cn(
                      'text-muted-foreground',
                      size === 'sm' ? 'text-xs' : 'text-xs'
                    )}
                  >
                    {getStatusText(presence?.status || 'offline')}
                    {showLastSeen &&
                      presence?.lastSeen &&
                      presence.status === 'offline' && (
                        <span> • {formatLastSeen(presence.lastSeen)}</span>
                      )}
                  </div>
                )}
              </div>
            )}
          </div>
        </TooltipTrigger>

        <TooltipContent>
          <div className="text-center">
            <div className="font-medium">{userName}</div>
            <div className="text-xs text-muted-foreground">
              {getStatusText(presence?.status || 'offline')}
              {presence?.lastSeen && presence.status === 'offline' && (
                <div>Last seen {formatLastSeen(presence.lastSeen)}</div>
              )}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
