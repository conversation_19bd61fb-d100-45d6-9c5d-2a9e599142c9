import React from 'react';
import { useTypingIndicators } from '@/hooks/useForum';
import { cn } from '@/lib/utils';

interface TypingIndicatorProps {
  groupId?: string;
  recipientId?: string;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  groupId,
  recipientId,
  className,
}) => {
  const typingUsers = useTypingIndicators(groupId, recipientId);

  if (typingUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0].userName} is typing...`;
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0].userName} and ${typingUsers[1].userName} are typing...`;
    } else {
      return `${typingUsers[0].userName} and ${typingUsers.length - 1} others are typing...`;
    }
  };

  return (
    <div
      className={cn(
        'flex items-center gap-2 px-4 py-2 text-sm text-muted-foreground',
        className
      )}
    >
      <div className="flex gap-1">
        <div
          className="w-1 h-1 bg-current rounded-full animate-bounce"
          style={{ animationDelay: '0ms' }}
        />
        <div
          className="w-1 h-1 bg-current rounded-full animate-bounce"
          style={{ animationDelay: '150ms' }}
        />
        <div
          className="w-1 h-1 bg-current rounded-full animate-bounce"
          style={{ animationDelay: '300ms' }}
        />
      </div>
      <span>{getTypingText()}</span>
    </div>
  );
};
