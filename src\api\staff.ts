import useSWR from 'swr';
import { useAuthSWR } from './useAuthSWR';

export const GetRoles = () => {
  const { data, isLoading } = useAuthSWR(`/admin/roles`);

  return {
    roles: data,
    roleLoading: isLoading,
  };
};

export const GetSpecialty = () => {
  const { data, isLoading, mutate } = useSWR(`/staff/list-specialty`);

  return {
    specialty: data,
    specialtyLoading: isLoading,
    mutate: mutate,
  };
};

//Admin profile data
export const GetProfile = () => {
  const { data, isLoading, error } = useAuthSWR('/staff/profile', {
    refreshInterval: 0,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    revalidateOnMount: true, // ✅ force fetch on mount
    dedupingInterval: 0,
  });

  // console.log(
  //   '[GetProfile] data:',
  //   data,
  //   'isLoading:',
  //   isLoading,
  //   'error:',
  //   error
  // );

  return {
    profile: data,
    isLoading: isLoading,
    error: error,
  };
};

// Get staff list
export const GetStaffList = () => {
  const { data, isLoading, error } = useAuthSWR('/staff/list');

  return {
    staffList: data?.data || [],
    isLoading,
    error,
  };
};
