import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  TrendingUp,
  Users,
  MessageCircle,
  Trophy,
  FileText,
  Calendar,
  Clock,
  Target,
  Award,
  Activity,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
} from 'lucide-react';
import { GetForumAnalytics } from '@/api/forum/data';
import {
  ResponsiveContainer,
  Line<PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart as RechartsBarChart,
  <PERSON>,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Cell,
  Pie,
  Legend,
} from 'recharts';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface ForumAnalyticsProps {
  className?: string;
}

const COLORS = [
  '#BD9A3D',
  '#2196F3',
  '#1A1A1A',
  '#4CAF50',
  '#FF9800',
  '#9C27B0',
  '#F44336',
];

export const ForumAnalytics: React.FC<ForumAnalyticsProps> = ({
  className,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');

  const { analytics, isLoading } = GetForumAnalytics(timeRange);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Forum Analytics</h2>
          <p className="text-muted-foreground">
            Insights into forum usage, engagement, and performance
          </p>
        </div>

        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24h</SelectItem>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Messages
            </CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics?.totalMessages || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span
                className={cn(
                  analytics?.messageGrowth >= 0
                    ? 'text-green-600'
                    : 'text-red-600'
                )}
              >
                {analytics?.messageGrowth >= 0 ? '+' : ''}
                {formatPercentage(analytics?.messageGrowth || 0)}
              </span>{' '}
              from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics?.activeUsers || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span
                className={cn(
                  analytics?.userGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                )}
              >
                {analytics?.userGrowth >= 0 ? '+' : ''}
                {formatPercentage(analytics?.userGrowth || 0)}
              </span>{' '}
              from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Games Played</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics?.gamesPlayed || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span
                className={cn(
                  analytics?.gameGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                )}
              >
                {analytics?.gameGrowth >= 0 ? '+' : ''}
                {formatPercentage(analytics?.gameGrowth || 0)}
              </span>{' '}
              from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Survey Responses
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatNumber(analytics?.surveyResponses || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              <span
                className={cn(
                  analytics?.surveyGrowth >= 0
                    ? 'text-green-600'
                    : 'text-red-600'
                )}
              >
                {analytics?.surveyGrowth >= 0 ? '+' : ''}
                {formatPercentage(analytics?.surveyGrowth || 0)}
              </span>{' '}
              from last period
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="games">Games</TabsTrigger>
          <TabsTrigger value="surveys">Surveys</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsLineChart data={analytics?.activityTimeline || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="messages"
                    stroke="#BD9A3D"
                    strokeWidth={2}
                    name="Messages"
                  />
                  <Line
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="#2196F3"
                    strokeWidth={2}
                    name="Active Users"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Groups */}
          <Card>
            <CardHeader>
              <CardTitle>Most Active Groups</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.topGroups?.map((group: any, index: number) => (
                  <div
                    key={group.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                          index === 0 && 'bg-yellow-500 text-white',
                          index === 1 && 'bg-gray-400 text-white',
                          index === 2 && 'bg-amber-600 text-white',
                          index > 2 && 'bg-muted text-muted-foreground'
                        )}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{group.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {group.memberCount} members
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{group.messageCount}</div>
                      <div className="text-sm text-muted-foreground">
                        messages
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-6">
          {/* User Engagement */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Message Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <RechartsPieChart>
                    <Pie
                      data={analytics?.messageDistribution || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {analytics?.messageDistribution?.map(
                        (entry: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        )
                      )}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Peak Activity Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <RechartsBarChart data={analytics?.peakHours || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="activity" fill="#BD9A3D" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Contributors */}
          <Card>
            <CardHeader>
              <CardTitle>Top Contributors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.topContributors?.map((user: any, index: number) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                          index === 0 && 'bg-yellow-500 text-white',
                          index === 1 && 'bg-gray-400 text-white',
                          index === 2 && 'bg-amber-600 text-white',
                          index > 2 && 'bg-muted text-muted-foreground'
                        )}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {user.department}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{user.messageCount}</div>
                      <div className="text-sm text-muted-foreground">
                        messages
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="games" className="space-y-6">
          {/* Game Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Prize Pool
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ₦{formatNumber(analytics?.totalPrizePool || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Distributed to winners
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Average Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analytics?.averageScore || 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all games
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Completion Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatPercentage(analytics?.gameCompletionRate || 0)}
                </div>
                <p className="text-xs text-muted-foreground">Games completed</p>
              </CardContent>
            </Card>
          </div>

          {/* Popular Games */}
          <Card>
            <CardHeader>
              <CardTitle>Most Popular Games</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.popularGames?.map((game: any, index: number) => (
                  <div
                    key={game.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                          index === 0 && 'bg-yellow-500 text-white',
                          index === 1 && 'bg-gray-400 text-white',
                          index === 2 && 'bg-amber-600 text-white',
                          index > 2 && 'bg-muted text-muted-foreground'
                        )}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{game.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {game.difficulty} • {game.totalQuestions} questions
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{game.participantCount}</div>
                      <div className="text-sm text-muted-foreground">
                        participants
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="surveys" className="space-y-6">
          {/* Survey Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Response Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatPercentage(analytics?.surveyResponseRate || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Average across surveys
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Completion Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analytics?.averageCompletionTime || 0}min
                </div>
                <p className="text-xs text-muted-foreground">
                  Average time to complete
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Surveys
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analytics?.activeSurveys || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently running
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Surveys */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Surveys</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics?.recentSurveys?.map((survey: any) => (
                  <div
                    key={survey.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex-1">
                      <div className="font-medium">{survey.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {survey.questionCount} questions • Created{' '}
                        {formatDistanceToNow(new Date(survey.createdAt), {
                          addSuffix: true,
                        })}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">{survey.responseCount}</div>
                      <div className="text-sm text-muted-foreground">
                        responses
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
