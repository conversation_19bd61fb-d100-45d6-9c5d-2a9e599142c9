'use client';

import { useState } from 'react';
import {
  Coffee,
  Package,
  MenuSquare,
  ShoppingCart,
  Users,
  UserRound,
  Star,
} from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import InventoryManagement from './inventory';
import MenuManagement from './menu';
import OrdersManagement from './orders';
import SalesManagement from './sales';

export default function CafeteriaContent() {
  const [activeTab, setActiveTab] = useState('inventory');
  const [orderType, setOrderType] = useState<'general' | 'staff' | 'special'>(
    'staff'
  );

  const canViewCafeteria = hasPermission(PERMISSIONS.CAFETERIA_VIEW);
  const canManageInventory = hasPermission(
    PERMISSIONS.CAFETERIA_INVENTORY_MANAGE
  );
  const canManageMenu = hasPermission(PERMISSIONS.CAFETERIA_MENU_MANAGE);
  const canManageOrders = hasPermission(PERMISSIONS.CAFETERIA_ORDERS_MANAGE);
  const canAccessPOS = hasPermission(PERMISSIONS.CAFETERIA_POS_ACCESS);

  if (!canViewCafeteria) {
    return (
      <div className="flex items-center justify-center h-full">
        You don't have permission to view the cafeteria management.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Coffee className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Cafeteria Management
      </h2>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full animate-in fade-in-50"
      >
        <TabsList className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 mb-6">
          <TabsTrigger
            value="inventory"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
            disabled={!canManageInventory}
          >
            <Package
              className="w-4 h-4"
              style={{
                color: activeTab === 'inventory' ? '#BD9A3D' : 'inherit',
              }}
            />
            Inventory Management
          </TabsTrigger>
          <TabsTrigger
            value="menu"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
            disabled={!canManageMenu}
          >
            <MenuSquare
              className="w-4 h-4"
              style={{ color: activeTab === 'menu' ? '#BD9A3D' : 'inherit' }}
            />
            Menu Management
          </TabsTrigger>
          <TabsTrigger
            value="orders"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
            disabled={!canManageOrders}
          >
            <ShoppingCart
              className="w-4 h-4"
              style={{ color: activeTab === 'orders' ? '#BD9A3D' : 'inherit' }}
            />
            Orders Management
          </TabsTrigger>
          <TabsTrigger
            value="sales"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
            disabled={!canAccessPOS}
          >
            <span
              style={{ color: activeTab === 'sales' ? '#BD9A3D' : 'inherit' }}
            >
              &#8358;
            </span>
            Sales Management
          </TabsTrigger>
        </TabsList>

        {/* Inventory Management Tab */}
        <TabsContent
          value="inventory"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <Package className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Inventory Management
            </h3>
            <div>{canManageInventory && <InventoryManagement />}</div>
          </div>
        </TabsContent>

        {/* Menu Management Tab */}
        <TabsContent
          value="menu"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <MenuSquare className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Menu Management
            </h3>
            <div>{canManageMenu && <MenuManagement />}</div>
          </div>
        </TabsContent>

        {/* Orders Management Tab */}
        <TabsContent
          value="orders"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <ShoppingCart className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Orders Management
            </h3>

            {/* Order Type Toggle */}
            <div className="flex justify-center mb-4">
              <TabsList className="grid grid-cols-3 w-1/2">
                <TabsTrigger
                  value="general"
                  className="flex items-center gap-2"
                  onClick={() => setOrderType('general')}
                  data-state={orderType === 'general' ? 'active' : ''}
                >
                  <Users className="w-4 h-4" />
                  General
                </TabsTrigger>
                <TabsTrigger
                  value="staff"
                  className="flex items-center gap-2"
                  onClick={() => setOrderType('staff')}
                  data-state={orderType === 'staff' ? 'active' : ''}
                >
                  <UserRound className="w-4 h-4" />
                  Staff
                </TabsTrigger>
                <TabsTrigger
                  value="special"
                  className="flex items-center gap-2"
                  onClick={() => setOrderType('special')}
                  data-state={orderType === 'special' ? 'active' : ''}
                >
                  <Star className="w-4 h-4" />
                  Special
                </TabsTrigger>
              </TabsList>
            </div>

            <div>
              {canManageOrders && <OrdersManagement orderType={orderType} />}
            </div>
          </div>
        </TabsContent>

        {/* Sales Management Tab */}
        <TabsContent
          value="sales"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                  <span
              style={{ color: activeTab === 'sales' ? '#BD9A3D' : 'inherit' }}
            >
              &#8358;
            </span>
              Sales Management
            </h3>

            <div>{canAccessPOS && <SalesManagement />}</div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
