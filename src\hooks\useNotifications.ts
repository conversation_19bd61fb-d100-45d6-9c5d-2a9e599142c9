'use client';

import { useSnapshot } from 'valtio';
import { notificationStore } from '@/store/notificationStore';

export function useNotifications() {
  const {
    notifications,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
  } = useSnapshot(notificationStore);

  const unreadCount = notifications.filter((n) => !n.read).length;

  return {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
  };
}
