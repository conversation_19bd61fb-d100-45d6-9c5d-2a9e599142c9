import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Search,
  Trophy,
  Clock,
  Users,
  Play,
  FileText,
  Calendar,
  Target,
  Award,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react';
import { GetGames } from '@/api/games/data';
import { GetSurveys } from '@/api/surveys/data';
import { CreateSurveyModal } from '@/components/surveys/CreateSurveyModal';
import { QuizGame } from '@/components/games/QuizGame';
import { SurveyParticipation } from '@/components/surveys/SurveyParticipation';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface GamesAndSurveysProps {
  className?: string;
}

export const GamesAndSurveys: React.FC<GamesAndSurveysProps> = ({
  className,
}) => {
  const [activeTab, setActiveTab] = useState('games');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateSurvey, setShowCreateSurvey] = useState(false);
  const [selectedGame, setSelectedGame] = useState<any>(null);
  const [selectedSurvey, setSelectedSurvey] = useState<any>(null);
  const [gameFilter, setGameFilter] = useState<'all' | 'active' | 'completed'>(
    'all'
  );
  const [surveyFilter, setSurveyFilter] = useState<
    'all' | 'active' | 'completed'
  >('all');

  const { games, isLoading: gamesLoading } = GetGames();
  const { surveys, isLoading: surveysLoading } = GetSurveys();

  const filteredGames =
    games?.filter((game: any) => {
      const matchesSearch =
        game.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        game.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = gameFilter === 'all' || game.status === gameFilter;
      return matchesSearch && matchesFilter;
    }) || [];

  const filteredSurveys =
    surveys?.filter((survey: any) => {
      const matchesSearch =
        survey.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        survey.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter =
        surveyFilter === 'all' || survey.status === surveyFilter;
      return matchesSearch && matchesFilter;
    }) || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'scheduled':
        return 'bg-green-500';
      case 'completed':
        return 'bg-blue-500';
      case 'draft':
        return 'bg-yellow-500';
      case 'cancelled':
      case 'archived':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-50 dark:bg-green-950';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-950';
      case 'hard':
        return 'text-red-600 bg-red-50 dark:bg-red-950';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-950';
    }
  };

  if (selectedGame) {
    return (
      <div className={className}>
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={() => setSelectedGame(null)}
            className="mb-4"
          >
            ← Back to Games
          </Button>
        </div>
        <QuizGame
          game={selectedGame}
          onGameComplete={() => setSelectedGame(null)}
          onExit={() => setSelectedGame(null)}
        />
      </div>
    );
  }

  if (selectedSurvey) {
    return (
      <div className={className}>
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={() => setSelectedSurvey(null)}
            className="mb-4"
          >
            ← Back to Surveys
          </Button>
        </div>
        <SurveyParticipation
          survey={selectedSurvey}
          onComplete={() => setSelectedSurvey(null)}
          onExit={() => setSelectedSurvey(null)}
        />
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Games & Surveys</h2>
          <p className="text-muted-foreground">
            Participate in quizzes, games, and surveys
          </p>
        </div>

        <Button onClick={() => setShowCreateSurvey(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Survey
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search games and surveys..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="games" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Games
          </TabsTrigger>
          <TabsTrigger value="surveys" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Surveys
          </TabsTrigger>
        </TabsList>

        <TabsContent value="games" className="space-y-4">
          {/* Game Filters */}
          <div className="flex gap-2">
            <Button
              variant={gameFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('all')}
            >
              All Games
            </Button>
            <Button
              variant={gameFilter === 'active' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('active')}
            >
              Active
            </Button>
            <Button
              variant={gameFilter === 'completed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('completed')}
            >
              Completed
            </Button>
          </div>

          {/* Games Grid */}
          {gamesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGames.map((game: any) => (
                <Card
                  key={game.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{game.name}</CardTitle>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {game.description}
                        </p>
                      </div>
                      <div
                        className={cn(
                          'w-3 h-3 rounded-full',
                          getStatusColor(game.status)
                        )}
                      />
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>
                          {game.currentParticipants}/{game.maxParticipants}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{game.duration}min</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Target className="h-4 w-4" />
                        <span>{game.totalQuestions}Q</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={getDifficultyColor(game.difficulty)}
                        >
                          {game.difficulty}
                        </Badge>
                        <Badge variant="secondary">
                          ₦{game.prizePool.toLocaleString()}
                        </Badge>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(game.createdAt), {
                          addSuffix: true,
                        })}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        className="flex-1"
                        onClick={() => setSelectedGame(game)}
                        disabled={game.status !== 'active'}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        {game.status === 'active' ? 'Play Now' : 'View'}
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!gamesLoading && filteredGames.length === 0 && (
            <div className="text-center py-12">
              <Trophy className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No games found</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'No games available at the moment'}
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="surveys" className="space-y-4">
          {/* Survey Filters */}
          <div className="flex gap-2">
            <Button
              variant={surveyFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSurveyFilter('all')}
            >
              All Surveys
            </Button>
            <Button
              variant={surveyFilter === 'active' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSurveyFilter('active')}
            >
              Active
            </Button>
            <Button
              variant={surveyFilter === 'completed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSurveyFilter('completed')}
            >
              Completed
            </Button>
          </div>

          {/* Surveys Grid */}
          {surveysLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredSurveys.map((survey: any) => (
                <Card
                  key={survey.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">
                          {survey.title}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {survey.description}
                        </p>
                      </div>
                      <div
                        className={cn(
                          'w-3 h-3 rounded-full',
                          getStatusColor(survey.status)
                        )}
                      />
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <FileText className="h-4 w-4" />
                        <span>{survey.questions.length} questions</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{survey.totalResponses} responses</span>
                      </div>
                      {survey.endDate && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>
                            Ends{' '}
                            {formatDistanceToNow(new Date(survey.endDate), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={survey.isAnonymous ? 'secondary' : 'outline'}
                        >
                          {survey.isAnonymous ? 'Anonymous' : 'Identified'}
                        </Badge>
                        <Badge variant="outline">{survey.targetAudience}</Badge>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        by {survey.createdByName}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        className="flex-1"
                        onClick={() => setSelectedSurvey(survey)}
                        disabled={survey.status !== 'active'}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        {survey.status === 'active' ? 'Take Survey' : 'View'}
                      </Button>
                      <Button variant="outline" size="sm">
                        <TrendingUp className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!surveysLoading && filteredSurveys.length === 0 && (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No surveys found</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'No surveys available at the moment'}
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Create Survey Modal */}
      <CreateSurveyModal
        isOpen={showCreateSurvey}
        onClose={() => setShowCreateSurvey(false)}
        onSurveyCreated={() => {
          setShowCreateSurvey(false);
          // Refresh surveys list
        }}
      />
    </div>
  );
};
