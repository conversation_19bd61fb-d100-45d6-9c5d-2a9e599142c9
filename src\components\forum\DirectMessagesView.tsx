import React, { useEffect, useState, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Search, Paperclip, Send } from 'lucide-react';
import { format } from 'date-fns';
import { GetProfile } from '@/api/staff';
import { GetDirectMessages, sendDirectMessage } from '@/api/forum/data';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

interface Staff {
  id: string;
  name: string;
  avatar?: string;
  role: string;
}

// This would typically come from an API call
const mockStaffMembers: Staff[] = [
  { id: '1', name: '<PERSON>', role: 'Doctor' },
  { id: '2', name: '<PERSON>', role: '<PERSON>' },
  { id: '3', name: '<PERSON>', role: '<PERSON><PERSON>' },
  { id: '4', name: '<PERSON>', role: 'Receptionist' },
];

export const DirectMessagesView: React.FC = () => {
  const { profile } = GetProfile();
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSending, setIsSending] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    messages,
    isLoading: isMessagesLoading,
    mutate: mutateMessages,
  } = GetDirectMessages(selectedStaff?.id || '');

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const canSendMessage = hasPermission(PERMISSIONS.FORUM_SEND_MESSAGE);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleMessageUpdated = () => {
    mutateMessages();
  };

  const handleMessageSent = () => {
    mutateMessages();
  };

  const filteredStaff = mockStaffMembers.filter(
    (staff) =>
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      staff.id !== profile?.data?.id
  );

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const isOwnMessage = (message: any) => {
    return profile?.data?.id === message.senderId;
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments((prev) => [...prev, ...newFiles]);
    }
  };

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return;
    if (!selectedStaff) return;

    setIsSending(true);

    try {
      await sendDirectMessage(selectedStaff.id, message, attachments);
      setMessage('');
      setAttachments([]);
      mutateMessages();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const formatMessageTime = (dateString: string) => {
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <div className="flex-1 flex flex-col md:flex-row h-full bg-background">
      <div className="md:w-[350px] border-b md:border-b-0 md:border-r flex flex-col bg-background">
        <div className="p-3 md:p-4 bg-primary text-primary-foreground">
          <h2 className="text-base md:text-lg font-semibold mb-2 md:mb-4">
            Direct Messages
          </h2>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search staff..."
              className="pl-8 text-sm bg-background border-0 rounded-md"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="flex md:flex-col overflow-x-auto md:overflow-y-auto md:flex-1">
          {filteredStaff.map((staff) => (
            <div
              key={staff.id}
              className={`shrink-0 md:w-full flex items-center p-3 border-b cursor-pointer hover:bg-muted ${selectedStaff?.id === staff.id ? 'bg-muted' : ''}`}
              onClick={() => setSelectedStaff(staff)}
            >
              <Avatar className="h-10 w-10 mr-3">
                <AvatarImage src={staff.avatar} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {getInitials(staff.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-center">
                  <p className="font-medium truncate">{staff.name}</p>
                  <span className="text-xs text-muted-foreground">
                    12:30 PM
                  </span>
                </div>
                <p className="text-sm text-muted-foreground truncate">
                  {staff.role}
                </p>
              </div>
            </div>
          ))}

          {filteredStaff.length === 0 && (
            <div className="text-sm text-muted-foreground p-4">
              No staff members found
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col bg-muted/30 bg-[url('/whatsapp-bg.png')] bg-repeat">
        {selectedStaff ? (
          <>
            <div className="bg-background p-3 md:p-4 flex items-center shadow-sm border-b">
              <Avatar className="h-10 w-10 mr-3">
                <AvatarImage src={selectedStaff.avatar} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {getInitials(selectedStaff.name)}
                </AvatarFallback>
              </Avatar>
              <div className="overflow-hidden flex-1">
                <h2 className="font-semibold truncate">{selectedStaff.name}</h2>
                <p className="text-xs md:text-sm text-muted-foreground truncate">
                  {selectedStaff.role}
                </p>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {isMessagesLoading ? (
                <div className="flex justify-center p-4">
                  Loading messages...
                </div>
              ) : messages && messages.length > 0 ? (
                messages.map((message: any) => (
                  <div key={message.id} className="mb-4">
                    <div
                      className={`max-w-[80%] rounded-lg p-2 md:p-3 ${
                        isOwnMessage(message)
                          ? 'ml-auto bg-primary/20 rounded-tr-none'
                          : 'mr-auto bg-background rounded-tl-none'
                      }`}
                    >
                      {!isOwnMessage(message) && (
                        <p className="text-xs font-medium text-primary mb-1">
                          {message.senderName}
                        </p>
                      )}
                      <p className="text-sm md:text-base break-words">
                        {message.content}
                      </p>
                      <div className="flex justify-end items-center mt-1">
                        <span className="text-[10px] md:text-xs text-muted-foreground">
                          {formatMessageTime(message.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground p-4 bg-background bg-opacity-70 rounded-lg">
                  No messages yet. Start the conversation!
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {canSendMessage && (
              <div className="p-2 md:p-3 bg-background border-t">
                <div className="flex items-end gap-2 bg-muted rounded-full p-1 pl-4">
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Type a message..."
                    className="min-h-[40px] md:min-h-[24px] text-sm md:text-base resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 py-2 bg-transparent"
                    disabled={isSending}
                  />

                  <div className="flex gap-1 pr-1">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      className="h-10 w-10 rounded-full"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isSending}
                    >
                      <Paperclip className="h-5 w-5" />
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        className="hidden"
                        multiple
                      />
                    </Button>

                    <Button
                      type="button"
                      size="icon"
                      className="h-10 w-10 rounded-full bg-primary hover:bg-primary/90"
                      onClick={handleSend}
                      disabled={
                        (!message.trim() && attachments.length === 0) ||
                        isSending
                      }
                    >
                      <Send className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center bg-background">
            <div className="w-40 h-40 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-4">
              <Search className="h-16 w-16 text-primary" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Start Messaging</h2>
            <p className="text-muted-foreground text-center max-w-md">
              Select a staff member from the list to start a conversation
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
