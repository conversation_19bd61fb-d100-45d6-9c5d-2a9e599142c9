'use client';

import { useEffect, useCallback } from 'react';
import { useSnapshot } from 'valtio';
import websocketService from '@/services/websocket';
import { forumStore } from '@/store/forumStore';

export function useWebSocket() {
  const { isConnected, connectionStatus } = useSnapshot(forumStore);

  const on = useCallback((event: string, callback: (data: any) => void) => {
    websocketService.on(event, callback);

    // Return cleanup function
    return () => {
      websocketService.off(event, callback);
    };
  }, []);

  const off = useCallback((event: string, callback: (data: any) => void) => {
    websocketService.off(event, callback);
  }, []);

  const connect = useCallback(() => {
    websocketService.connect();
  }, []);

  const disconnect = useCallback(() => {
    websocketService.disconnect();
  }, []);

  return {
    isConnected,
    connectionStatus,
    on,
    off,
    connect,
    disconnect,
  };
}
