'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search } from 'lucide-react';
import { GetTransactions } from '@/api/data';
import { numberFormat } from '@/lib/utils';
import { StatusBadge } from '@/components/common/status-badge';
import { Input } from '@/components/ui/input';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';

const TransactionData = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [typeFilter, setTypeFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Update query parameters when search term, type filter, or date range changes
  useEffect(() => {
    let params = [];

    if (typeFilter) {
      params.push(typeFilter);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, typeFilter, startDate, endDate, setQueryParam]);

  const { transactions, transactionLoading } = GetTransactions(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const data = transactions?.data?.transactions;
  const totalPages = transactions?.data?.totalPages ?? 0;

  const handleEventFromModal = (transaction: any) => {
    setDetail(transaction);
    setOpen(true);
  };

  const buttonOptions = [
    { label: 'All', param: '', className: '' },
    {
      label: 'Inbound',
      param: 'type=INBOUND',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
    {
      label: 'Outbound',
      param: 'type=OUTBOUND',
      className:
        'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400',
    },
  ];

  const handleSelect = (param: string) => {
    setTypeFilter(param);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="space-x-2">
            {buttonOptions.map(({ label, param, className }) => (
              <StatusBadge
                status={label.toLowerCase()}
                className={`cursor-pointer ${className} ${typeFilter === param ? 'ring-2 ring-offset-1' : ''}`}
                key={label}
                onClick={() => handleSelect(param)}
              />
            ))}
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search transactions..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Transaction Date</th>
              {/* <th className="table-style">Package Amount</th> */}
              <th className="table-style">Amount Paid</th>
              <th className="table-style">Reference</th>
              <th className="table-style">Mode</th>
              <th className="table-style">Type</th>
              <th className="table-style">Status</th>
              <th className="table-style">More</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {data?.map((transaction: any, index: number) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={transaction.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(transaction.createdAt).format('MMMM D, YYYY h:mm A')}
                </td>
                {/* <td className="table-style">
                  {transaction.user.emailAddress} /{' '}
                  {transaction.user.phoneNumber}
                </td> */}
                {/* <td className="table-style">
                  {numberFormat(transaction.bookedBy.amount)}
                </td> */}
                <td className="table-style">
                  {numberFormat(transaction.amount)}
                </td>
                <td className="table-style">{transaction.reference}</td>
                <td className="table-style">{transaction.mode}</td>
                <td className="table-style">
                  {transaction.type === 'INBOUND' ? (
                    <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
                      Inbound
                    </span>
                  ) : (
                    <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400">
                      Outbound
                    </span>
                  )}
                </td>

                <td className="table-style">
                  {StatusBadge({
                    status: transaction.status.toLowerCase(),
                  })}
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => handleEventFromModal(transaction)}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {totalPages === 0 && (
          <div className="border text-sm py-12 text-center dark:text-white">
            No data available
          </div>
        )}
        {transactionLoading && (
          <div className="bg-white py-24 text-center">
            <Loader2 className="animate-spin w-6 h-6 " />
            <span>Loading...</span>
          </div>
        )}
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Transactions"
          totalPages={totalPages}
          currentPage={currentPage}
          totalCount={transactions?.data?.totalCount}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && <Details open={open} setOpen={setOpen} data={detail} />}
    </>
  );
};

export default TransactionData;
