import { z } from 'zod';

export const FeedbackFormSchema = z.object({
  title: z.string().min(3, {
    message: 'Title must be at least 3 characters.',
  }),
  message: z.string().min(10, {
    message: 'Message must be at least 10 characters.',
  }),
  status: z.string({
    required_error: 'Please select a status',
  }),
  response: z.string().optional(),
  email: z
    .string()
    .email({
      message: 'Please enter a valid email address.',
    })
    .optional(),
  category: z.string({
    required_error: 'Please select a category',
  }),
});

export type FeedbackFormValues = z.infer<typeof FeedbackFormSchema>;
