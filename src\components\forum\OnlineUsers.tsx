import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useOnlineStatus } from '@/hooks/useForum';
import { cn } from '@/lib/utils';
import { MessageCircle, Users } from 'lucide-react';

interface OnlineUsersProps {
  groupId?: string;
  onStartDirectMessage?: (userId: string) => void;
  className?: string;
}

export const OnlineUsers: React.FC<OnlineUsersProps> = ({
  groupId,
  onStartDirectMessage,
  className,
}) => {
  const { onlineUsers, onlineCount } = useOnlineStatus();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      default:
        return 'Offline';
    }
  };

  return (
    <div
      className={cn(
        'w-64 bg-background border-l flex flex-col h-full',
        className
      )}
    >
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-2">
          <Users className="h-4 w-4" />
          <h3 className="font-medium">Online Users</h3>
          <Badge variant="secondary" className="ml-auto">
            {onlineCount}
          </Badge>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {onlineUsers.map((user: any) => (
            <div
              key={user.userId}
              className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors group"
            >
              <div className="relative">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback className="text-xs">
                    {user.userName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div
                  className={cn(
                    'absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-background',
                    getStatusColor(user.status)
                  )}
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">
                  {user.userName}
                </div>
                <div className="text-xs text-muted-foreground">
                  {getStatusText(user.status)}
                </div>
              </div>

              {onStartDirectMessage && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                  onClick={() => onStartDirectMessage(user.userId)}
                >
                  <MessageCircle className="h-3 w-3" />
                </Button>
              )}
            </div>
          ))}

          {onlineUsers.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No users online</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
