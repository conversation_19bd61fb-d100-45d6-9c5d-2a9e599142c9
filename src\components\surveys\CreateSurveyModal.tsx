import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  CalendarIcon,
  Plus,
  Trash2,
  GripVertical,
  Users,
  Settings,
  Eye,
  Clock,
} from 'lucide-react';
import { format } from 'date-fns';
import { Survey, SurveyQuestion, createSurvey } from '@/api/surveys/data';
import { GetStaffList } from '@/api/staff';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface CreateSurveyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSurveyCreated?: (survey: Survey) => void;
}

const QUESTION_TYPES = [
  {
    value: 'multiple_choice',
    label: 'Multiple Choice',
    description: 'Select multiple options',
  },
  {
    value: 'single_choice',
    label: 'Single Choice',
    description: 'Select one option',
  },
  { value: 'text', label: 'Short Text', description: 'Single line text input' },
  {
    value: 'textarea',
    label: 'Long Text',
    description: 'Multi-line text input',
  },
  { value: 'rating', label: 'Rating Scale', description: '1-5 or 1-10 rating' },
  {
    value: 'yes_no',
    label: 'Yes/No',
    description: 'Simple yes or no question',
  },
  { value: 'date', label: 'Date', description: 'Date picker' },
  { value: 'number', label: 'Number', description: 'Numeric input' },
  { value: 'email', label: 'Email', description: 'Email address input' },
];

export const CreateSurveyModal: React.FC<CreateSurveyModalProps> = ({
  isOpen,
  onClose,
  onSurveyCreated,
}) => {
  const [activeTab, setActiveTab] = useState('basic');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    isAnonymous: false,
    allowMultipleResponses: false,
    requireLogin: true,
    targetAudience: 'all' as 'all' | 'department' | 'role' | 'custom',
    targetCriteria: [] as string[],
    settings: {
      showProgressBar: true,
      allowBackNavigation: true,
      randomizeQuestions: false,
      showResultsAfterSubmission: false,
      sendNotificationEmails: true,
      collectRespondentInfo: true,
    },
  });

  const [questions, setQuestions] = useState<Partial<SurveyQuestion>[]>([
    {
      type: 'single_choice',
      question: '',
      description: '',
      isRequired: true,
      order: 0,
      options: [
        { id: '1', text: 'Option 1', value: 'option1', order: 0 },
        { id: '2', text: 'Option 2', value: 'option2', order: 1 },
      ],
    },
  ]);

  const { staffList } = GetStaffList();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    if (name.startsWith('settings.')) {
      const settingName = name.replace('settings.', '');
      setFormData((prev) => ({
        ...prev,
        settings: { ...prev.settings, [settingName]: checked },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: checked }));
    }
  };

  const addQuestion = () => {
    const newQuestion: Partial<SurveyQuestion> = {
      type: 'single_choice',
      question: '',
      description: '',
      isRequired: true,
      order: questions.length,
      options: [
        { id: '1', text: 'Option 1', value: 'option1', order: 0 },
        { id: '2', text: 'Option 2', value: 'option2', order: 1 },
      ],
    };
    setQuestions([...questions, newQuestion]);
  };

  const removeQuestion = (index: number) => {
    setQuestions(questions.filter((_, i) => i !== index));
  };

  const updateQuestion = (index: number, updates: Partial<SurveyQuestion>) => {
    const updatedQuestions = [...questions];
    updatedQuestions[index] = { ...updatedQuestions[index], ...updates };
    setQuestions(updatedQuestions);
  };

  const addOption = (questionIndex: number) => {
    const question = questions[questionIndex];
    if (question.options) {
      const newOption = {
        id: Date.now().toString(),
        text: `Option ${question.options.length + 1}`,
        value: `option${question.options.length + 1}`,
        order: question.options.length,
      };
      updateQuestion(questionIndex, {
        options: [...question.options, newOption],
      });
    }
  };

  const removeOption = (questionIndex: number, optionIndex: number) => {
    const question = questions[questionIndex];
    if (question.options) {
      const updatedOptions = question.options.filter(
        (_, i) => i !== optionIndex
      );
      updateQuestion(questionIndex, { options: updatedOptions });
    }
  };

  const updateOption = (
    questionIndex: number,
    optionIndex: number,
    text: string
  ) => {
    const question = questions[questionIndex];
    if (question.options) {
      const updatedOptions = [...question.options];
      updatedOptions[optionIndex] = {
        ...updatedOptions[optionIndex],
        text,
        value: text.toLowerCase().replace(/\s+/g, '_'),
      };
      updateQuestion(questionIndex, { options: updatedOptions });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      toast.error('Survey title is required');
      return;
    }

    if (questions.length === 0) {
      toast.error('At least one question is required');
      return;
    }

    // Validate questions
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      if (!question.question?.trim()) {
        toast.error(`Question ${i + 1} text is required`);
        return;
      }
      if (
        (question.type === 'multiple_choice' ||
          question.type === 'single_choice') &&
        (!question.options || question.options.length < 2)
      ) {
        toast.error(`Question ${i + 1} must have at least 2 options`);
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const surveyData: Partial<Survey> = {
        ...formData,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        questions: questions as SurveyQuestion[],
      };

      const createdSurvey = await createSurvey(surveyData);

      if (onSurveyCreated) {
        onSurveyCreated(createdSurvey);
      }

      resetForm();
      onClose();
    } catch (error) {
      console.error('Error creating survey:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      isAnonymous: false,
      allowMultipleResponses: false,
      requireLogin: true,
      targetAudience: 'all',
      targetCriteria: [],
      settings: {
        showProgressBar: true,
        allowBackNavigation: true,
        randomizeQuestions: false,
        showResultsAfterSubmission: false,
        sendNotificationEmails: true,
        collectRespondentInfo: true,
      },
    });
    setQuestions([
      {
        type: 'single_choice',
        question: '',
        description: '',
        isRequired: true,
        order: 0,
        options: [
          { id: '1', text: 'Option 1', value: 'option1', order: 0 },
          { id: '2', text: 'Option 2', value: 'option2', order: 1 },
        ],
      },
    ]);
    setStartDate(undefined);
    setEndDate(undefined);
    setActiveTab('basic');
  };

  const needsOptions = (type: string) => {
    return ['multiple_choice', 'single_choice'].includes(type);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Survey</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="questions">Questions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="targeting">Targeting</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Survey Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter survey title"
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Enter survey description"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label>Start Date (Optional)</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'justify-start text-left font-normal',
                            !startDate && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, 'PPP') : 'Pick a date'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="grid gap-2">
                    <Label>End Date (Optional)</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'justify-start text-left font-normal',
                            !endDate && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, 'PPP') : 'Pick a date'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="questions" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Survey Questions</h3>
                <Button type="button" onClick={addQuestion} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Question
                </Button>
              </div>

              <div className="space-y-4">
                {questions.map((question, questionIndex) => (
                  <Card key={questionIndex}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          Question {questionIndex + 1}
                        </CardTitle>
                        <div className="flex items-center gap-2">
                          <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeQuestion(questionIndex)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-2">
                        <Label>Question Type</Label>
                        <Select
                          value={question.type}
                          onValueChange={(value) =>
                            updateQuestion(questionIndex, {
                              type: value as any,
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {QUESTION_TYPES.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div>
                                  <div className="font-medium">
                                    {type.label}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {type.description}
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid gap-2">
                        <Label>Question Text</Label>
                        <Textarea
                          value={question.question || ''}
                          onChange={(e) =>
                            updateQuestion(questionIndex, {
                              question: e.target.value,
                            })
                          }
                          placeholder="Enter your question"
                          rows={2}
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label>Description (Optional)</Label>
                        <Input
                          value={question.description || ''}
                          onChange={(e) =>
                            updateQuestion(questionIndex, {
                              description: e.target.value,
                            })
                          }
                          placeholder="Additional context or instructions"
                        />
                      </div>

                      {needsOptions(question.type || '') && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label>Answer Options</Label>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => addOption(questionIndex)}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Option
                            </Button>
                          </div>
                          <div className="space-y-2">
                            {question.options?.map((option, optionIndex) => (
                              <div
                                key={option.id}
                                className="flex items-center gap-2"
                              >
                                <Input
                                  value={option.text}
                                  onChange={(e) =>
                                    updateOption(
                                      questionIndex,
                                      optionIndex,
                                      e.target.value
                                    )
                                  }
                                  placeholder={`Option ${optionIndex + 1}`}
                                />
                                {question.options &&
                                  question.options.length > 2 && (
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() =>
                                        removeOption(questionIndex, optionIndex)
                                      }
                                      className="text-destructive hover:text-destructive"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        <Switch
                          id={`required-${questionIndex}`}
                          checked={question.isRequired || false}
                          onCheckedChange={(checked) =>
                            updateQuestion(questionIndex, {
                              isRequired: checked,
                            })
                          }
                        />
                        <Label htmlFor={`required-${questionIndex}`}>
                          Required
                        </Label>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Survey Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Anonymous Responses</Label>
                        <p className="text-sm text-muted-foreground">
                          Don't collect respondent identity
                        </p>
                      </div>
                      <Switch
                        checked={formData.isAnonymous}
                        onCheckedChange={(checked) =>
                          handleSwitchChange('isAnonymous', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Allow Multiple Responses</Label>
                        <p className="text-sm text-muted-foreground">
                          Let users submit multiple times
                        </p>
                      </div>
                      <Switch
                        checked={formData.allowMultipleResponses}
                        onCheckedChange={(checked) =>
                          handleSwitchChange('allowMultipleResponses', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Show Progress Bar</Label>
                        <p className="text-sm text-muted-foreground">
                          Display completion progress
                        </p>
                      </div>
                      <Switch
                        checked={formData.settings.showProgressBar}
                        onCheckedChange={(checked) =>
                          handleSwitchChange(
                            'settings.showProgressBar',
                            checked
                          )
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Allow Back Navigation</Label>
                        <p className="text-sm text-muted-foreground">
                          Let users go back to previous questions
                        </p>
                      </div>
                      <Switch
                        checked={formData.settings.allowBackNavigation}
                        onCheckedChange={(checked) =>
                          handleSwitchChange(
                            'settings.allowBackNavigation',
                            checked
                          )
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Randomize Questions</Label>
                        <p className="text-sm text-muted-foreground">
                          Show questions in random order
                        </p>
                      </div>
                      <Switch
                        checked={formData.settings.randomizeQuestions}
                        onCheckedChange={(checked) =>
                          handleSwitchChange(
                            'settings.randomizeQuestions',
                            checked
                          )
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Send Notification Emails</Label>
                        <p className="text-sm text-muted-foreground">
                          Notify staff about new survey
                        </p>
                      </div>
                      <Switch
                        checked={formData.settings.sendNotificationEmails}
                        onCheckedChange={(checked) =>
                          handleSwitchChange(
                            'settings.sendNotificationEmails',
                            checked
                          )
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="targeting" className="space-y-4">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label>Target Audience</Label>
                  <Select
                    value={formData.targetAudience}
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        targetAudience: value as any,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Staff</SelectItem>
                      <SelectItem value="department">
                        Specific Department
                      </SelectItem>
                      <SelectItem value="role">Specific Role</SelectItem>
                      <SelectItem value="custom">Custom Selection</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.targetAudience !== 'all' && (
                  <div className="grid gap-2">
                    <Label>Select Target Staff</Label>
                    <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                      {staffList?.map((staff: any) => (
                        <div
                          key={staff.id}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded-sm"
                        >
                          <div className="flex items-center gap-2">
                            <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-xs font-medium">
                                {staff.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <span className="text-sm font-medium">
                                {staff.name}
                              </span>
                              <p className="text-xs text-muted-foreground">
                                {staff.department} • {staff.role}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={formData.targetCriteria.includes(staff.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setFormData((prev) => ({
                                  ...prev,
                                  targetCriteria: [
                                    ...prev.targetCriteria,
                                    staff.id,
                                  ],
                                }));
                              } else {
                                setFormData((prev) => ({
                                  ...prev,
                                  targetCriteria: prev.targetCriteria.filter(
                                    (id) => id !== staff.id
                                  ),
                                }));
                              }
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Survey'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
