'use client';

import React, { useEffect } from 'react';
import websocketService from '@/services/websocket';
import { forumStore } from '@/store/forumStore';

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    // Set initial connection status
    forumStore.setConnectionStatus('connecting');

    // Connect to WebSocket when the provider mounts
    websocketService.connect();

    // Set up connection status listeners
    const handleConnected = () => {
      forumStore.setConnectionStatus('connected');
      // Update user presence to online when connected
      websocketService.updatePresence('online');
    };

    const handleDisconnected = () => {
      forumStore.setConnectionStatus('disconnected');
    };

    websocketService.on('connected', handleConnected);
    websocketService.on('disconnected', handleDisconnected);

    // Handle page visibility changes to update presence
    const handleVisibilityChange = () => {
      if (document.hidden) {
        websocketService.updatePresence('away');
      } else {
        websocketService.updatePresence('online');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Disconnect when the provider unmounts
    return () => {
      websocketService.off('connected', handleConnected);
      websocketService.off('disconnected', handleDisconnected);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      // Update presence to offline before disconnecting
      websocketService.updatePresence('offline');
      websocketService.disconnect();
    };
  }, []);

  return <>{children}</>;
};

export default WebSocketProvider;
