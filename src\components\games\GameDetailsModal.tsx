import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Clock,
  Trophy,
  Users,
  Target,
  Calendar,
  Award,
  Play,
  Pause,
  StopCircle,
  Edit,
  Trash2,
  Copy,
  Settings,
} from 'lucide-react';
import { Game } from '@/api/games/data';

interface GameDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  game: any;
}

export const GameDetailsModal: React.FC<GameDetailsModalProps> = ({
  isOpen,
  onClose,
  game,
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'scheduled':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl">{game.name}</DialogTitle>
              <div className="flex items-center gap-2 mt-2">
                <Badge className={getStatusColor(game.status)}>
                  {game.status.charAt(0).toUpperCase() + game.status.slice(1)}
                </Badge>
                <Badge
                  variant="outline"
                  className={getDifficultyColor(game.difficulty)}
                >
                  {game.difficulty.charAt(0).toUpperCase() +
                    game.difficulty.slice(1)}
                </Badge>
                <Badge variant="outline">
                  {game.category.charAt(0).toUpperCase() +
                    game.category.slice(1)}
                </Badge>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm">
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="questions">Questions</TabsTrigger>
            <TabsTrigger value="participants">Participants</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Game Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Participants
                      </p>
                      <p className="text-2xl font-bold">
                        {game.participantCount || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Duration</p>
                      <p className="text-2xl font-bold">{game.duration}m</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Trophy className="h-5 w-5 text-yellow-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Prize Pool
                      </p>
                      <p className="text-2xl font-bold">
                        {formatCurrency(game.prizePool || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-sm text-muted-foreground">Questions</p>
                      <p className="text-2xl font-bold">
                        {game.questions?.length || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Game Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Game Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Description</p>
                    <p>{game.description || 'No description provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Created By</p>
                    <p>{game.createdBy || 'System'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Created At</p>
                    <p>{formatDate(game.createdAt)}</p>
                  </div>
                  {game.scheduledFor && (
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Scheduled For
                      </p>
                      <p>{formatDate(game.scheduledFor)}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Game Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Max Participants
                    </span>
                    <span>{game.maxParticipants || 'Unlimited'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Passing Score
                    </span>
                    <span>{game.passingScore || 70}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Public Game
                    </span>
                    <Badge variant={game.isPublic ? 'default' : 'secondary'}>
                      {game.isPublic ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      Allow Hints
                    </span>
                    <Badge variant={game.allowHints ? 'default' : 'secondary'}>
                      {game.allowHints ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="questions" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                Questions ({game.questions?.length || 0})
              </h3>
              <Button size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit Questions
              </Button>
            </div>

            <div className="space-y-4">
              {game.questions?.map((question: any, index: number) => (
                <Card key={question.id}>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline">Question {index + 1}</Badge>
                      <div className="flex gap-2 text-sm text-muted-foreground">
                        <span>{question.points} pts</span>
                        <span>{question.timeLimit}s</span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="font-medium mb-3">{question.question}</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {question.options.map(
                        (option: any, optionIndex: number) => (
                          <div
                            key={optionIndex}
                            className={`p-2 rounded border ${
                              optionIndex === question.correctAnswer
                                ? 'bg-green-50 border-green-200 text-green-800'
                                : 'bg-gray-50 border-gray-200'
                            }`}
                          >
                            <span className="text-sm">
                              {String.fromCharCode(65 + optionIndex)}. {option}
                            </span>
                            {optionIndex === question.correctAnswer && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Correct
                              </Badge>
                            )}
                          </div>
                        )
                      )}
                    </div>
                  </CardContent>
                </Card>
              )) || (
                <div className="text-center py-8 text-muted-foreground">
                  No questions added yet
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="participants" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                Participants ({game.participantCount || 0})
              </h3>
            </div>

            <div className="space-y-2">
              {/* Mock participants data */}
              {Array.from({ length: 5 }).map((_, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={`/avatars/user-${index + 1}.jpg`} />
                          <AvatarFallback>U{index + 1}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">User {index + 1}</p>
                          <p className="text-sm text-muted-foreground">
                            user{index + 1}@example.com
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">Joined</p>
                        <p className="text-sm">2 hours ago</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Game Results</h3>
            </div>

            {game.status === 'completed' ? (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Leaderboard</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded"
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                              {index + 1}
                            </div>
                            <Avatar>
                              <AvatarFallback>U{index + 1}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">User {index + 1}</p>
                              <p className="text-sm text-muted-foreground">
                                {Math.floor(Math.random() * 30) + 70}% accuracy
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold">
                              {Math.floor(Math.random() * 500) + 500} pts
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {formatCurrency(
                                Math.floor(Math.random() * 5000) + 1000
                              )}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Results will be available after the game is completed
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
