'use client';

import React from 'react';
import { DirectMessagesView } from '@/components/forum/DirectMessagesView';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function DirectMessagesPage() {
  const canViewMessages = hasPermission(PERMISSIONS.FORUM_VIEW_MESSAGES);

  if (!canViewMessages) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            You don&apos;t have permission to view messages.
          </p>
        </div>
      </div>
    );
  }

  const router = useRouter();

  return (
    <div className="flex flex-col h-full">
      <div className="md:hidden p-3 bg-primary text-primary-foreground flex items-center">
        <Button
          variant="ghost"
          size="sm"
          className="p-0 h-8 w-8 text-primary-foreground hover:bg-primary/80"
          onClick={() => router.push('/forum')}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-lg font-semibold ml-2">Messages</h1>
      </div>
      <DirectMessagesView />
    </div>
  );
}
