'use client';

import React, { ReactNode, useState, useEffect } from 'react';
import { useRoleAccess } from '@/lib/auth/roleAccess';
import { hasPermission } from '@/lib/types/permissions';

interface ProtectedComponentProps {
  children: ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

/**
 * A component wrapper that only renders its children if the user has the required roles and permissions
 */
export function ProtectedComponent({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  fallback = null,
}: ProtectedComponentProps) {
  // Check for role-based access
  const { isLoading, hasAccess } = useRoleAccess(
    requiredRoles.length > 0 ? requiredRoles : ['superadmin', 'admin']
  );

  // Check for permission-based access
  const hasAllPermissions =
    requiredPermissions.length === 0 ||
    requiredPermissions.every((permission) => hasPermission(permission));
  const [showLoading, setShowLoading] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isLoading) {
      timer = setTimeout(() => setShowLoading(true), 500);
    } else {
      setShowLoading(false);
    }
    return () => clearTimeout(timer);
  }, [isLoading]);

  if (isLoading) {
    if (!showLoading) {
      return <>{children}</>;
    }

    return (
      <div className="flex items-center justify-center p-4">
        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  // Only render children if user has both role access and all required permissions
  if (hasAccess && hasAllPermissions) {
    return <>{children}</>;
  }

  // Otherwise render the fallback
  return <>{fallback}</>;
}

/**
 * A component that only renders if the user has the specified permission
 */
export function PermissionGuard({
  children,
  permission,
  fallback = null,
}: {
  children: ReactNode;
  permission: string;
  fallback?: ReactNode;
}) {
  if (hasPermission(permission)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}
