'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ArrowLeft } from 'lucide-react';
import { GetIssuedStock } from '@/api/cafeteria/data';
import dayjs from 'dayjs';

interface ViewSuppliesProps {
  selectedItem: any;
  onBack: () => void;
}

export function ViewIssued({ selectedItem, onBack }: ViewSuppliesProps) {
  const [pageSize, setPageSize] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);

  const { stock, stockLoading } = GetIssuedStock(
    `?page=${currentPage}&limit=${pageSize}&issueId=${selectedItem.id}`
  );

  const stockData = stock?.data?.stock;
  console.log(stockData);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Inventory
        </Button>
        <h1 className="text-2xl font-bold">
          Stock Issue History - {selectedItem?.itemName}
        </h1>
      </div>

      <div className="border rounded-md p-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Comment</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stockData?.length > 0 ? (
              stockData?.map((issue: any, index: number) => (
                <TableRow key={issue.id}>
                  <TableCell>
                    {' '}
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(issue.date).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>{issue.quantity}</TableCell>
                  <TableCell>{issue.comment}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No issue history found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
