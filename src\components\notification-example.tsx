'use client';

import { Button } from '@/components/ui/button';
import { useNotifications } from '@/hooks/useNotifications';

export function NotificationExample() {
  const { addNotification } = useNotifications();

  const handleAddNotification = () => {
    // Add a notification to the in-app notification system
    addNotification({
      title: 'New Message',
      message: 'You have a new message in your inbox',
      link: '/dashboard',
    });
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Notification Demo</h3>
      <p className="text-sm text-muted-foreground">
        Click the button below to add a test notification. The notification bell
        in the top navigation will show a badge with the count of unread
        notifications.
      </p>
      <Button onClick={handleAddNotification} variant="default" size="sm">
        Send Test Notification
      </Button>
    </div>
  );
}
