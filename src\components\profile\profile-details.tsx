'use client';

import React from 'react';
import { Modal } from '@/components/common/modal';
import { UserProps } from './types';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, Calendar, Mail, Shield, Clock } from 'lucide-react';
import dayjs from 'dayjs';
import { formatRoleNames } from '@/lib/utils';

interface ProfileDetailsProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: UserProps;
}

const ProfileDetails: React.FC<ProfileDetailsProps> = ({
  open,
  setOpen,
  user,
}) => {
  if (!user) return null;

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A';
    return dayjs(date).format('MMM DD, YYYY hh:mm A');
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Profile Details"
      description="Your account information"
      size="md"
    >
      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal Info
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Account Info
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-4">
          <div className="space-y-4">
            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Full Name
              </span>
              <span className="font-semibold">{user.fullName || 'N/A'}</span>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Designation
              </span>
              <div className="flex items-center gap-2">
                <span>{user.role}</span>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Email Address
              </span>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{user.email || 'N/A'}</span>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Role
              </span>
              <Badge variant="outline" className="w-fit">
                {formatRoleNames(user.roles)}
              </Badge>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="account" className="space-y-4">
          <div className="space-y-4">
            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Account Status
              </span>
              <Badge
                variant={user.isActive ? 'success' : 'destructive'}
                className="w-fit"
              >
                {user.isActive !== undefined
                  ? user.isActive
                    ? 'Active'
                    : 'Inactive'
                  : 'Unknown'}
              </Badge>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Account Created
              </span>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(user.createdAt)}</span>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Last Login
              </span>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(user.lastLogin)}</span>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-muted-foreground">
                Last Password Reset
              </span>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(user.dateResetPassword)}</span>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Modal>
  );
};

export default ProfileDetails;
