'use client';

import { GetProfile } from '@/api/staff';

export const PERMISSIONS = {
  DASHBOARD_VIEW: 'dashboard:view',

  PACKAGE_VIEW: 'package:view',
  PACKAGE_CREATE: 'package:create',
  PACKAGE_EDIT: 'package:edit',
  PACKAGE_DELETE: 'package:delete',

  BOOKING_VIEW: 'booking:view',
  BOOKING_UPDATE: 'booking:update',

  LOCATION_CREATE: 'location:create',
  LOCATION_EDIT: 'location:edit',
  LOCATION_DELETE: 'location:delete',
  LOCATION_ALL: 'location:all',
  LOCATION_HQ: 'location:hq',
  LOCATION_GUDU: 'location:gudu',
  LOCATION_VI: 'location:vi',
  LOCATION_ZUNGERU: 'location:zungeru',
  LOCATION_JEBBA: 'location:jebba',
  LOCATION_KAINJI: 'location:kainji',
  LOCATION_GWARINPA: 'location:gwarinpa',

  INCIDENT_CREATE: 'incident:create',
  INCIDENT_VIEW: 'incident:view',
  INCIDENT_EDIT: 'incident:edit',
  INCIDENT_SUBMIT: 'incident:submit',
  INCIDENT_APPROVE: 'incident:approve',
  INCIDENT_DELETE: 'incident:delete',
  INCIDENT_COMMENT: 'incident:comment',
  INCIDENT_ASSIGN: 'incident:assign',
  INCIDENT_CLOSE: 'incident:close',

  REWARD_VIEW: 'reward:view',
  REWARD_CREATE: 'reward:create',
  REWARD_EDIT: 'reward:edit',
  REWARD_DELETE: 'reward:delete',

  FEEDBACK_VIEW: 'feedback:view',

  REFERRAL_VIEW: 'referral:view',
  REFERRAL_CREATE: 'referral:create',
  REFERRAL_EDIT: 'referral:edit',
  REFERRAL_DELETE: 'referral:delete',

  TRANSACTION_VIEW: 'transaction:view',
  TRANSACTION_CREATE: 'transaction:create',
  TRANSACTION_EDIT: 'transaction:edit',

  STAFF_CREATE: 'staff:create',
  STAFF_VIEW: 'staff:view',
  STAFF_EDIT: 'staff:edit',
  STAFF_DELETE: 'staff:delete',

  SETTINGS_VIEW: 'settings:view',
  SETTINGS_CREATE: 'settings:create',
  SETTINGS_EDIT: 'settings:edit',
  SETTINGS_DELETE: 'settings:delete',

  REQUISITION_CREATE: 'requisition:create',
  REQUISITION_VIEW: 'requisition:view',
  REQUISITION_EDIT: 'requisition:edit',
  REQUISITION_SUBMIT: 'requisition:submit',
  REQUISITION_APPROVE: 'requisition:approve',
  REQUISITION_DELETE: 'requisition:delete',
  REQUISITION_CANCEL: 'requisition:cancel',

  FORUM_VIEW: 'forum:view',
  FORUM_CREATE_GROUP: 'forum:create_group ',
  FORUM_VIEW_GROUP: 'forum:view_group',
  FORUM_EDIT_GROUP: 'forum:edit_group',
  FORUM_DELETE_GROUP: 'forum:delete_group',
  FORUM_ADD_MEMBERS: 'forum:add_members',
  FORUM_REMOVE_MEMBERS: 'forum:remove_members',
  FORUM_JOIN_GROUP: 'forum:join_group',
  FORUM_SEND_MESSAGE: 'forum:send_message',
  FORUM_VIEW_MESSAGES: 'forum:view_messages',
  FORUM_EDIT_MESSAGE: 'forum:edit_message',
  FORUM_DELETE_MESSAGE: 'forum:delete_message',
  FORUM_MODERATE_MESSAGES: 'forum:moderate_messages',
  FORUM_PIN_MESSAGE: 'forum:pin_message',

  PATIENT_VIEW: 'patient:view',
  PATIENT_CREATE: 'patient:create',
  PATIENT_EDIT: 'patient:edit',
  PATIENT_DELETE: 'patient:delete',

  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_EDIT: 'role:edit',
  ROLE_DELETE: 'role:delete',

  PERMISSION_VIEW: 'permission:view',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_EDIT: 'permission:edit',
  PERMISSION_DELETE: 'permission:delete',

  INTERACTION_VIEW: 'interaction:view',
  INTERACTION_CREATE: 'interaction:create',
  INTERACTION_EDIT: 'interaction:edit',
  INTERACTION_REPLY: 'interaction:reply',

  CAFETERIA_VIEW: 'cafeteria:view',
  CAFETERIA_INVENTORY_MANAGE: 'cafeteria:inventory_manage',
  CAFETERIA_MENU_MANAGE: 'cafeteria:menu_manage',
  CAFETERIA_ORDERS_MANAGE: 'cafeteria:orders_manage',
  CAFETERIA_POS_ACCESS: 'cafeteria:pos_access',
};

export function hasPermission(permission: string): boolean {
  const { profile, isLoading } = GetProfile();

  // During initial loading, be permissive to avoid flickering UI
  // This is safe because the layout component will handle the overall auth check
  if (isLoading) {
    return true;
  }

  // Check if user has roles (new structure) or role (old structure)
  const roles =
    profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

  if (!profile || !profile.data || !roles.length) {
    console.warn(
      'Profile or roles not available for permission check:',
      permission
    );
    return false;
  }

  // Check if any of the user's roles has the required permission
  return roles.some((role: { permissions: { action: string }[] }) =>
    role?.permissions?.some(
      (item: { action: string }) => item.action === permission
    )
  );
}

export function isProfileLoaded(): boolean {
  const { profile, isLoading, error } = GetProfile();

  // If still loading, consider it as "not yet loaded" but not a failure
  if (isLoading) return true; // Changed to true to prevent premature redirects
  `
  // Only return false if we have a definitive negative result
  // (profile loaded but invalid or error occurred)`;
  if (error) return false;
  if (profile === undefined) return true; // Still loading or not yet available

  // Check for either roles (new structure) or role (old structure)
  if (
    profile &&
    (!profile.data ||
      ((!profile.data.roles || profile.data.roles.length === 0) &&
        !profile.data.role))
  )
    return false;

  // Profile is loaded and valid
  return true;
}
